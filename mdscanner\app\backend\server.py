import sys
import os
import threading
import time
import queue
from flask import Flask, Response, render_template, jsonify, send_from_directory
from flask_socketio import SocketIO, emit
import cv2
import numpy as np
from io import BytesIO

# 添加项目根目录到Python路径，以便导入mdscanner
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from mdscanner import MdScanner
from app.backend.image_processor import nv12_to_jpeg

app = Flask(__name__, static_folder='../frontend')
app.config['SECRET_KEY'] = 'your-secret-key'  # 在生产环境中应该使用更安全的密钥
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
scanner = None
is_streaming = False
streaming_thread = None
image_queue = queue.Queue(maxsize=10)  # 用于存储图像数据的队列

# 设备IP地址
DEVICE_IP = "************"  # 我的这台设备
# DEVICE_IP = "*************"

def init_scanner():
    """初始化扫描仪连接"""
    global scanner
    try:
        scanner = MdScanner(DEVICE_IP)
        print(f"已连接到设备: {DEVICE_IP}")
        return True
    except Exception as e:
        print(f"连接设备失败: {e}")
        return False

def start_scanner():
    """启动扫描仪"""
    global scanner
    if not scanner:
        print("扫描仪未初始化")
        return False
    
    try:
        # 使用事件来等待回调
        start_event = threading.Event()
        start_result = None

        def _start_callback_(ret):
            nonlocal start_result
            start_result = ret
            start_event.set()
            
        scanner.MdScanner_Start(_start_callback_)
        
        # 等待回调事件，设置更长的超时时间
        if start_event.wait(timeout=10):
            if start_result == 0:
                print("扫描仪启动成功")
                return True
            else:
                print(f"扫描仪启动失败，返回码: {start_result}")
                return False
        else:
            print("扫描仪启动超时")
            return False
    except Exception as e:
        print(f"启动扫描仪时发生异常: {e}")
        return False

def stop_scanner():
    """停止扫描仪"""
    global scanner, is_streaming
    if not scanner:
        print("扫描仪未初始化")
        return
    
    is_streaming = False
    if streaming_thread and streaming_thread.is_alive():
        streaming_thread.join()
    
    try:
        # 停止连续捕获
        scanner.stop_continuous_capture()
        
        # 使用事件来等待回调
        stop_event = threading.Event()
        stop_result = None

        def _stop_callback_(ret):
            nonlocal stop_result
            stop_result = ret
            stop_event.set()
            
        scanner.MdScanner_Stop(_stop_callback_)
        
        # 等待回调事件
        if stop_event.wait(timeout=10):
            if stop_result == 0:
                print("扫描仪停止成功")
            else:
                print(f"扫描仪停止失败，返回码: {stop_result}")
        else:
            print("扫描仪停止超时")
    except Exception as e:
        print(f"停止扫描仪时发生异常: {e}")

def image_callback(width, height, format, size, out, ret):
    """图像回调函数"""
    global is_streaming, image_queue
    if not is_streaming or ret != 0 or len(out) == 0:
        return
    
    if format == 0:  # NV12格式
        # 将图像数据放入队列
        try:
            image_queue.put((out, width, height), block=False)
        except queue.Full:
            # 队列已满，丢弃旧数据
            try:
                image_queue.get_nowait()
            except queue.Empty:
                pass
            image_queue.put((out, width, height), block=False)

def image_stream_generator():
    """图像流生成器"""
    global scanner, is_streaming, image_queue
    is_streaming = True
    
    last_time = time.time()
    frame_count = 0
    
    while is_streaming:
        try:
            if scanner:
                # 从队列中获取图像数据
                try:
                    out, width, height = image_queue.get(timeout=1)
                    jpeg_data = nv12_to_jpeg(out, width, height)
                    if jpeg_data:
                        # 计算 FPS
                        frame_count += 1
                        current_time = time.time()
                        elapsed_time = current_time - last_time
                        if elapsed_time >= 1.0:
                            fps = frame_count / elapsed_time
                            last_time = current_time
                            frame_count = 0
                            # 通过WebSocket发送图像和FPS数据
                            socketio.emit('image_data', {'image': jpeg_data.getvalue(), 'fps': f"{fps:.2f}"})
                        else:
                            # 只发送图像数据
                            socketio.emit('image_data', {'image': jpeg_data.getvalue(), 'fps': None})
                except queue.Empty:
                    pass
            else:
                time.sleep(1)
        except Exception as e:
            print(f"图像流生成错误: {e}")
            import traceback
            traceback.print_exc()
            time.sleep(1)

@app.route('/')
def index():
    """主页"""
    return send_from_directory(app.static_folder, 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """提供静态文件"""
    return send_from_directory(app.static_folder, filename)

@app.route('/start')
def start_stream():
    """开始视频流"""
    global is_streaming, streaming_thread
    
    if is_streaming:
        return jsonify({'status': 'error', 'message': '视频流已在运行'})
    
    if not scanner:
        if not init_scanner():
            return jsonify({'status': 'error', 'message': '无法连接到设备'})
    
    if not start_scanner():
        return jsonify({'status': 'error', 'message': '无法启动扫描仪'})
    
    # 启动连续图像捕获
    scanner.start_continuous_capture(image_callback, 1024*1280*2)
    
    # 启动图像流线程
    is_streaming = True
    streaming_thread = threading.Thread(target=image_stream_generator)
    streaming_thread.daemon = True
    streaming_thread.start()
    
    return jsonify({'status': 'success', 'message': '视频流已启动'})

@app.route('/stop')
def stop_stream():
    """停止视频流"""
    global is_streaming
    
    if not is_streaming:
        return jsonify({'status': 'error', 'message': '视频流未运行'})
    
    stop_scanner()
    is_streaming = False
    
    return jsonify({'status': 'success', 'message': '视频流已停止'})

@socketio.on('connect')
def handle_connect():
    """处理WebSocket连接"""
    print('客户端已连接')

@socketio.on('disconnect')
def handle_disconnect():
    """处理WebSocket断开连接"""
    print('客户端已断开连接')

if __name__ == '__main__':
    # 初始化扫描仪
    # 注意：在实际应用中，可能需要在用户请求时才初始化
    # init_scanner()
    
    # 启动Flask-SocketIO服务器
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)