import React, { memo } from 'react';
import { useScanner } from '../contexts/ScannerContext';
import { Button, Spin } from 'antd';
import { VideoCameraOutlined } from '@ant-design/icons';

// 优化的图像组件，避免不必要的重渲染
const StreamImage = memo(({ src }: { src: string }) => (
  <img
    src={src}
    alt="Video Stream"
    style={{
      maxWidth: '100%',
      maxHeight: '100%',
      objectFit: 'contain' // 保持宽高比
    }}
  />
));

const VideoStreamPlayer: React.FC = () => {
  const { isConnected, isStreaming, frameData, startStream, stopStream } = useScanner();

  const renderContent = () => {
    if (!isConnected) {
      return <p>设备未连接</p>;
    }
    if (isStreaming) {
      if (frameData) {
        return <StreamImage src={frameData} />;
      }
      return <Spin tip="正在加载视频流..." size="large" />;
    }
    return <p>视频流已停止</p>;
  };

  return (
    <div style={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#000', color: '#fff' }}>
      <div style={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%' }}>
        {renderContent()}
      </div>
      {isConnected && (
        <div style={{ padding: '10px' }}>
          <Button
            type="primary"
            icon={<VideoCameraOutlined />}
            onClick={isStreaming ? stopStream : startStream}
            danger={isStreaming}
          >
            {isStreaming ? '停止推流' : '开始推流'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default VideoStreamPlayer;