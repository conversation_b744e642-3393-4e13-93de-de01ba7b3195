import React, { memo, useCallback, useState } from 'react';
import { useScanner } from '../contexts/ScannerContext';
import { Button, Spin, Switch } from 'antd';
import { VideoCameraOutlined, DashboardOutlined } from '@ant-design/icons';
import { useVideoStreamFPS } from '../hooks/useVideoStreamFPS';
import FPSMonitor from './FPSMonitor';

const VideoStreamPlayer: React.FC = memo(() => {
  const { isConnected, isStreaming, frameData, startStream, stopStream } = useScanner();
  const [showFPSMonitor, setShowFPSMonitor] = useState(true);

  // 监控视频流FPS
  const { stats: videoStreamStats, resetStats } = useVideoStreamFPS(
    frameData,
    isStreaming,
    { enabled: showFPSMonitor }
  );

  const renderContent = useCallback(() => {
    if (!isConnected) {
      return <p>设备未连接</p>;
    }
    if (isStreaming) {
      if (frameData) {
        return (
          <img
            src={frameData}
            alt="Video Stream"
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              imageRendering: 'auto' // 优化图像渲染
            }}
          />
        );
      }
      return <Spin tip="正在加载视频流..." size="large" />;
    }
    return <p>视频流已停止</p>;
  }, [isConnected, isStreaming, frameData]);

  const handleStreamToggle = useCallback(() => {
    if (isStreaming) {
      stopStream();
    } else {
      startStream();
    }
  }, [isStreaming, startStream, stopStream]);

  return (
    <div style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#000',
      color: '#fff',
      position: 'relative'
    }}>
      {/* FPS监控器 - 紧凑模式 */}
      {showFPSMonitor && isStreaming && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          zIndex: 1000,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '12px',
          fontFamily: 'monospace',
          minWidth: '200px'
        }}>
          <div style={{ marginBottom: '4px', fontWeight: 'bold' }}>视频流性能监控</div>
          <div>WebSocket FPS: <span style={{ color: '#52c41a' }}>{videoStreamStats.wsMessageFPS.toFixed(1)}</span></div>
          <div>图像更新 FPS: <span style={{ color: '#1890ff' }}>{videoStreamStats.imageUpdateFPS.toFixed(1)}</span></div>
          <div>渲染 FPS: <span style={{ color: '#faad14' }}>{videoStreamStats.renderFPS.toFixed(1)}</span></div>
          <div>平均延迟: {videoStreamStats.avgLatency.toFixed(1)}ms</div>
          <div>重复帧: {videoStreamStats.duplicateFrames}</div>
          <div style={{ marginTop: '4px', fontSize: '10px', opacity: 0.8 }}>
            总消息: {videoStreamStats.wsMessageCount} | 总渲染: {videoStreamStats.renderCount}
          </div>
        </div>
      )}

      {/* FPS监控控制按钮 */}
      <div style={{
        position: 'absolute',
        top: 10,
        right: 10,
        zIndex: 1000
      }}>
        <Button
          size="small"
          icon={<DashboardOutlined />}
          onClick={() => setShowFPSMonitor(!showFPSMonitor)}
          style={{
            backgroundColor: showFPSMonitor ? '#1890ff' : 'rgba(255,255,255,0.2)',
            borderColor: showFPSMonitor ? '#1890ff' : 'rgba(255,255,255,0.3)',
            color: 'white'
          }}
        >
          FPS
        </Button>
        {showFPSMonitor && (
          <Button
            size="small"
            onClick={resetStats}
            style={{
              marginLeft: '8px',
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderColor: 'rgba(255,255,255,0.3)',
              color: 'white'
            }}
          >
            重置
          </Button>
        )}
      </div>

      <div style={{
        flexGrow: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%'
      }}>
        {renderContent()}
      </div>
      {isConnected && (
        <div style={{ padding: '10px' }}>
          <Button
            type="primary"
            icon={<VideoCameraOutlined />}
            onClick={handleStreamToggle}
            danger={isStreaming}
          >
            {isStreaming ? '停止推流' : '开始推流'}
          </Button>
        </div>
      )}
    </div>
  );
});

export default VideoStreamPlayer;