import React, { memo, useCallback } from 'react';
import { useScanner } from '../contexts/ScannerContext';
import { Button, Spin } from 'antd';
import { VideoCameraOutlined } from '@ant-design/icons';

const VideoStreamPlayer: React.FC = memo(() => {
  const { isConnected, isStreaming, frameData, startStream, stopStream } = useScanner();

  const renderContent = useCallback(() => {
    if (!isConnected) {
      return <p>设备未连接</p>;
    }
    if (isStreaming) {
      if (frameData) {
        return (
          <img
            src={frameData}
            alt="Video Stream"
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              imageRendering: 'auto' // 优化图像渲染
            }}
          />
        );
      }
      return <Spin tip="正在加载视频流..." size="large" />;
    }
    return <p>视频流已停止</p>;
  }, [isConnected, isStreaming, frameData]);

  const handleStreamToggle = useCallback(() => {
    if (isStreaming) {
      stopStream();
    } else {
      startStream();
    }
  }, [isStreaming, startStream, stopStream]);

  return (
    <div style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#000',
      color: '#fff'
    }}>
      <div style={{
        flexGrow: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%'
      }}>
        {renderContent()}
      </div>
      {isConnected && (
        <div style={{ padding: '10px' }}>
          <Button
            type="primary"
            icon={<VideoCameraOutlined />}
            onClick={handleStreamToggle}
            danger={isStreaming}
          >
            {isStreaming ? '停止推流' : '开始推流'}
          </Button>
        </div>
      )}
    </div>
  );
});

export default VideoStreamPlayer;