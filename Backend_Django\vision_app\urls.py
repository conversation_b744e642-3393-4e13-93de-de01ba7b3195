from django.urls import path
from . import views

app_name = 'vision_app'

urlpatterns = [

    path('models/', views.list_models, name='list_models'), # 指向更新后的视图函数
    path('models/upload/', views.AIModelUploadView.as_view(), name='upload_model'), # 新增：上传模型 API
    path('models/delete/', views.delete_models, name='delete_models'), # 新增：删除模型 API (需要管理员权限)
    path('models/<int:model_id>/update/', views.update_model, name='update_model'), # 新增：更新模型 API (需要管理员权限)
    path('ocr-tasks/', views.list_ocr_tasks, name='list_ocr_tasks'), # 新增：列出可用的OCR任务
    path('detect/barcode/ultralytics/', views.detect_barcode_ultralytics, name='detect_barcode_ultralytics'),

    # 其他特定模型的推理API可以按照类似 detect_barcode_ultralytics 的方式添加和修改
    path('detect/ocr/paddle/', views.detect_ocr_paddle_view, name='detect_ocr_paddle'), # 新增：PaddleOCR 推理 API
    path('restore/image/', views.AIImageRestoreView.as_view(), name='ai_image_restore'), # 新增：AI图像修复 API
    path('detect/feature-matching/traditional/', views.feature_matching_traditional_view, name='feature_matching_traditional'), # 新增：传统特征点匹配 API
    path('detect/feature-matching/model/', views.FeatureMatchingModelView.as_view(), name='feature_matching_model'), # 新增：基于模型的特征点匹配 API
    path('example-images/', views.list_example_images, name='list_example_images'), # 新增：获取示例图片列表 API
    
    # 管理员权限API
    path('example-images/upload/', views.upload_example_image, name='upload_example_image'), # 新增：上传示例图片（管理员）
    path('example-images/delete/', views.delete_example_images, name='delete_example_images'), # 新增：批量删除示例图片（管理员）
    path('example-images/update-order/', views.update_image_order, name='update_image_order'), # 新增：更新图片顺序（管理员）
    path('example-images/update-description/<int:image_id>/', views.update_example_image_description, name='update_example_image_description'), # 新增：更新图片描述（管理员）
    path('example-images/folders/create/', views.create_example_folder, name='create_example_folder'), # 新增：创建文件夹（管理员）
    path('example-images/folders/rename/', views.rename_example_folder, name='rename_example_folder'), # 新增：重命名文件夹（管理员）
    path('example-images/folders/delete/', views.delete_example_folder, name='delete_example_folder'), # 新增：删除文件夹（管理员）
    path('example-images/<str:category>/<path:filename>', views.serve_example_image, name='serve_example_image'), # 新增：提供示例图片文件服务
    # 管理员身份验证API
    path('admin/login/', views.admin_login, name='admin_login'), # 新增：管理员登录 API
    path('admin/logout/', views.admin_logout, name='admin_logout'), # 新增：管理员登出 API
    path('admin/status/', views.admin_check_status, name='admin_check_status'), # 新增：检查管理员状态 API

    # 扫描器 API
    path('scanner/connect/', views.scanner_connect, name='scanner_connect'),
    path('scanner/disconnect/', views.scanner_disconnect, name='scanner_disconnect'),
    path('scanner/start/', views.scanner_start_stream, name='scanner_start_stream'),
    path('scanner/stop/', views.scanner_stop_stream, name='scanner_stop_stream'),
    path('scanner/status/', views.scanner_status, name='scanner_status'),
    path('scanner/inference-frame/', views.scanner_get_inference_frame, name='scanner_get_inference_frame'),
]