import { useRef, useEffect, useState, useCallback } from 'react';

interface FPSStats {
  currentFPS: number;
  averageFPS: number;
  minFPS: number;
  maxFPS: number;
  frameCount: number;
  droppedFrames: number;
  renderTime: number; // 平均渲染时间(ms)
}

interface FPSMonitorOptions {
  updateInterval?: number; // FPS更新间隔(ms)
  maxSamples?: number; // 最大采样数
  enabled?: boolean; // 是否启用监控
}

export const useFPSMonitor = (options: FPSMonitorOptions = {}) => {
  const {
    updateInterval = 1000,
    maxSamples = 60,
    enabled = true
  } = options;

  const [fpsStats, setFpsStats] = useState<FPSStats>({
    currentFPS: 0,
    averageFPS: 0,
    minFPS: 0,
    maxFPS: 0,
    frameCount: 0,
    droppedFrames: 0,
    renderTime: 0
  });

  const frameTimesRef = useRef<number[]>([]);
  const lastFrameTimeRef = useRef<number>(0);
  const frameCountRef = useRef<number>(0);
  const droppedFramesRef = useRef<number>(0);
  const renderTimesRef = useRef<number[]>([]);
  const animationFrameRef = useRef<number>();
  const lastUpdateTimeRef = useRef<number>(0);

  const recordFrame = useCallback(() => {
    if (!enabled) return;

    const now = performance.now();
    const renderStartTime = now;

    // 记录帧时间
    if (lastFrameTimeRef.current > 0) {
      const frameTime = now - lastFrameTimeRef.current;
      frameTimesRef.current.push(frameTime);
      
      // 限制采样数量
      if (frameTimesRef.current.length > maxSamples) {
        frameTimesRef.current.shift();
      }

      // 检测丢帧 (如果帧间隔超过33ms，即低于30FPS)
      if (frameTime > 33) {
        droppedFramesRef.current++;
      }
    }

    frameCountRef.current++;
    lastFrameTimeRef.current = now;

    // 模拟渲染时间测量
    requestAnimationFrame(() => {
      const renderEndTime = performance.now();
      const renderTime = renderEndTime - renderStartTime;
      renderTimesRef.current.push(renderTime);
      
      if (renderTimesRef.current.length > maxSamples) {
        renderTimesRef.current.shift();
      }
    });

    // 定期更新FPS统计
    if (now - lastUpdateTimeRef.current >= updateInterval) {
      updateFPSStats();
      lastUpdateTimeRef.current = now;
    }

    // 继续监控
    animationFrameRef.current = requestAnimationFrame(recordFrame);
  }, [enabled, updateInterval, maxSamples]);

  const updateFPSStats = useCallback(() => {
    const frameTimes = frameTimesRef.current;
    if (frameTimes.length === 0) return;

    // 计算FPS
    const avgFrameTime = frameTimes.reduce((sum, time) => sum + time, 0) / frameTimes.length;
    const currentFPS = 1000 / avgFrameTime;
    
    // 计算统计数据
    const fpsSamples = frameTimes.map(time => 1000 / time);
    const averageFPS = fpsSamples.reduce((sum, fps) => sum + fps, 0) / fpsSamples.length;
    const minFPS = Math.min(...fpsSamples);
    const maxFPS = Math.max(...fpsSamples);

    // 计算平均渲染时间
    const renderTimes = renderTimesRef.current;
    const avgRenderTime = renderTimes.length > 0 
      ? renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length 
      : 0;

    setFpsStats({
      currentFPS: Math.round(currentFPS * 100) / 100,
      averageFPS: Math.round(averageFPS * 100) / 100,
      minFPS: Math.round(minFPS * 100) / 100,
      maxFPS: Math.round(maxFPS * 100) / 100,
      frameCount: frameCountRef.current,
      droppedFrames: droppedFramesRef.current,
      renderTime: Math.round(avgRenderTime * 100) / 100
    });
  }, []);

  const resetStats = useCallback(() => {
    frameTimesRef.current = [];
    frameCountRef.current = 0;
    droppedFramesRef.current = 0;
    renderTimesRef.current = [];
    lastFrameTimeRef.current = 0;
    lastUpdateTimeRef.current = 0;
    
    setFpsStats({
      currentFPS: 0,
      averageFPS: 0,
      minFPS: 0,
      maxFPS: 0,
      frameCount: 0,
      droppedFrames: 0,
      renderTime: 0
    });
  }, []);

  useEffect(() => {
    if (enabled) {
      lastUpdateTimeRef.current = performance.now();
      animationFrameRef.current = requestAnimationFrame(recordFrame);
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enabled, recordFrame]);

  return {
    fpsStats,
    resetStats,
    isMonitoring: enabled
  };
};
