body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: #333;
}

.controls {
    text-align: center;
    margin-bottom: 20px;
}

button {
    padding: 10px 20px;
    margin: 0 10px;
    font-size: 16px;
    cursor: pointer;
    border: none;
    border-radius: 5px;
    background-color: #007bff;
    color: white;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #0056b3;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.video-container {
    position: relative;
    text-align: center;
    margin-bottom: 20px;
    background-color: #000;
    border-radius: 5px;
    overflow: hidden;
    min-height: 480px; /* 根据需要调整 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-placeholder {
    color: #666;
    font-size: 1.2em;
}

#videoStream {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: opacity 0.5s ease-in-out;
}

#fpsDisplay {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 14px;
}

#status {
    text-align: center;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}