from django.http import JsonResponse
import json
import os
import logging
import tempfile
from django.conf import settings
import mimetypes
from urllib.parse import quote, unquote
from functools import wraps

from rest_framework.decorators import api_view
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser
from rest_framework import status

from .models import AIModel, ExampleImage
from .serializers import AIModelSerializer, AIModelUpdateSerializer
from .services import AdminService, PredictionService, ImageService
from collections import defaultdict

 # 获取一个logger实例
logger = logging.getLogger(__name__)

# --- 管理员权限验证装饰器 ---

def require_admin_auth(view_func):
    """
    管理员权限验证装饰器
    检查用户是否具有管理员权限
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # 检查会话中是否有管理员标识
        if not request.session.get('is_admin', False):
            logger.warning(f"Unauthorized admin access attempt to {view_func.__name__}")
            return Response({
                'success': False,
                'message': '需要管理员权限才能访问此功能'
            }, status=status.HTTP_403_FORBIDDEN)

        # 如果有管理员权限，继续执行原视图函数
        return view_func(request, *args, **kwargs)

    return wrapper

@api_view(['GET'])
def list_models(request): # 重命名函数
    """
    获取AI模型列表，并按模型类型 (model_type) 分组。
    可以通过以下查询参数来筛选模型：
    - 'model_scope':
        - 'system': (默认) 只返回系统模型 (is_system_model=True)。
        - 'custom': 只返回自定义模型 (is_system_model=False)。
        - 'all': 返回所有模型。
    - 'model_type_filter': 按指定的模型类型进行过滤 (例如, 'ocr', 'ai_restored').
    """
    try:
        model_scope = request.query_params.get('model_scope', 'system').lower()
        model_type_filter = request.query_params.get('model_type_filter', None)

        log_message_parts = [f"scope: '{model_scope}'"]
        if model_type_filter:
            log_message_parts.append(f"model_type_filter: '{model_type_filter}'")

        queryset = AIModel.objects.all()

        if model_scope == 'system':
            queryset = queryset.filter(is_system_model=True).order_by('name')
            logger.info(f"Initial filter: system models, ordered by name.")
        elif model_scope == 'custom':
            queryset = queryset.filter(is_system_model=False).order_by('-uploaded_at')
            logger.info(f"Initial filter: custom models, ordered by uploaded_at descending.")
        elif model_scope == 'all':
            queryset = queryset.order_by('-is_system_model', 'name')
            logger.info(f"Initial filter: all models, ordered by is_system_model then name.")
        else:
            logger.warning(f"Invalid model_scope '{model_scope}' provided. Defaulting to 'system'.")
            queryset = queryset.filter(is_system_model=True).order_by('name')
            # Update model_scope for logging consistency if it was invalid
            model_scope = 'system'
            log_message_parts[0] = f"scope: '{model_scope}' (defaulted)"


        if model_type_filter:
            queryset = queryset.filter(model_type=model_type_filter)
            logger.info(f"Applied model_type_filter: '{model_type_filter}'.")

        serializer = AIModelSerializer(queryset, many=True)

        grouped_models = defaultdict(list)
        for model_data in serializer.data:
            model_type_key = model_data.get('model_type') if model_data.get('model_type') is not None else 'unknown'
            grouped_models[model_type_key].append(model_data)

        logger.info(f"Successfully retrieved and grouped {len(queryset)} models for query ({', '.join(log_message_parts)}).")
        return Response(grouped_models, status=status.HTTP_200_OK)
    except Exception as e:
        current_scope_for_error = model_scope if 'model_scope' in locals() else "unknown"
        current_filter_for_error = model_type_filter if 'model_type_filter' in locals() and model_type_filter else "none"
        logger.error(f"Error retrieving models (scope: {current_scope_for_error}, filter: {current_filter_for_error}): {str(e)}", exc_info=True)
        return Response({'error': f'获取模型列表失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def detect_barcode_ultralytics(request):
    """
    条码检测API
    参数: image, model_name (可选), model_file (可选), confidence_threshold (可选), device (可选), preprocessing_method (可选)
    """
    logger.info("detect_barcode_ultralytics POST request received.")
    prediction_service = PredictionService()

    try:
        # 获取请求参数
        image_file = request.FILES.get('image')
        model_name = request.POST.get('model_name')
        model_file = request.FILES.get('model_file')
        confidence_threshold = float(request.POST.get('confidence_threshold', 0.25))
        device = request.POST.get('device', '')
        preprocessing_method = request.POST.get('preprocessing_method', 'full_scale')

        # 使用PredictionService处理条码检测
        result = prediction_service.predict_barcode_ultralytics(
            image_file=image_file,
            model_name=model_name,
            model_file=model_file,
            confidence_threshold=confidence_threshold,
            device=device,
            preprocessing_method=preprocessing_method
        )

        logger.info(f"Barcode detection completed: {result['total_detections']} detections")
        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for barcode detection: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in barcode detection: {str(e)}", exc_info=True)
        return Response({
            'error': f'条码检测失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AIModelUploadView(APIView):
    """
    AI模型上传API视图
    """
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        """
        AI模型上传API
        参数: model_file, name, model_type, version, description (可选), ocr_role (OCR模型必需), is_system_model (可选)
        """
        logger.info("AIModelUploadView POST request received.")
        admin_service = AdminService()

        try:
            # 使用AdminService处理模型上传
            result = admin_service.upload_ai_model(request.data)

            logger.info(f"AI model uploaded successfully: {result['model']['name']}")
            return Response(result, status=status.HTTP_201_CREATED)

        except ValueError as e:
            # 参数验证错误或模型已存在
            logger.warning(f"Invalid parameters for AI model upload: {str(e)}")
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error during AI model upload: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': f'处理模型上传失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def detect_ocr_paddle_view(request):
    """
    PaddleOCR文字识别API
    参数: image (图像文件), ocr_task_name (OCR任务名称), use_gpu (可选)
    """
    logger.info("detect_ocr_paddle_view POST request received.")
    prediction_service = PredictionService()

    try:
        # 获取请求参数
        image_file = request.FILES.get('image')
        if not image_file:
            logger.warning("No image file provided for OCR.")
            return Response({
                'error': '必须提供图像文件 (image)。'
            }, status=status.HTTP_400_BAD_REQUEST)

        ocr_task_name = request.POST.get('ocr_task_name')
        if not ocr_task_name:
            logger.warning("No ocr_task_name provided.")
            return Response({
                'error': '必须提供 ocr_task_name 参数。'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取GPU使用参数
        use_gpu = False
        if 'use_gpu' in request.POST:
            try:
                use_gpu = str(request.POST['use_gpu']).lower() in ('true', '1', 't')
                logger.info(f"GPU usage override: {use_gpu}")
            except Exception as e:
                logger.warning(f"Could not parse 'use_gpu' parameter: {e}")

        # 使用PredictionService执行OCR推理
        result = prediction_service.predict_ocr_paddle(
            image_file=image_file,
            ocr_task_name=ocr_task_name,
            use_gpu=use_gpu
        )

        # 转换结果格式以保持向后兼容
        compatible_result = {
            'task_name': ocr_task_name,
            'results': result['results'],
            'time_info': result['performance']
        }

        logger.info(f"OCR task '{ocr_task_name}' completed successfully")
        return Response(compatible_result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for OCR: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"OCR prediction failed: {str(e)}", exc_info=True)
        return Response({
            'error': f'OCR预测处理失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def list_ocr_tasks(request):
    """
    获取可用的系统OCR模型列表。
    一个OCR任务被认为是可用的，如果它同时具有 'detection' 和 'recognition' 角色的系统模型。
    现在基于ocr_collection_name进行分组，而不是name字段。
    """
    try:
        logger.info("Attempting to retrieve available OCR tasks.")
        # 筛选所有系统OCR模型
        system_ocr_models = AIModel.objects.filter(model_type='ocr', is_system_model=True)

        # 按OCR集合名称分组，如果没有collection_name则使用name
        collections_with_roles = defaultdict(lambda: {'detection': None, 'recognition': None, 'task_name': None})

        for model in system_ocr_models:
            # 使用ocr_collection_name作为分组键，如果为空则使用name
            collection_key = model.ocr_collection_name or model.name

            if model.ocr_role == 'detection':
                collections_with_roles[collection_key]['detection'] = model
            elif model.ocr_role == 'recognition':
                collections_with_roles[collection_key]['recognition'] = model

            # 保存task_name（用于API调用）
            if not collections_with_roles[collection_key]['task_name']:
                collections_with_roles[collection_key]['task_name'] = model.name

        available_tasks = []
        for collection_name, roles_data in collections_with_roles.items():
            if roles_data['detection'] and roles_data['recognition']:
                available_tasks.append({
                    'task_name': roles_data['task_name'],  # 用于API调用的原始name
                    'display_name': collection_name  # 用于显示的集合名称
                })

        # 按显示名称排序以获得一致的顺序
        available_tasks.sort(key=lambda x: x['display_name'])

        logger.info(f"Successfully retrieved {len(available_tasks)} available OCR tasks.")
        return Response(available_tasks, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error retrieving OCR tasks: {str(e)}", exc_info=True)
        return Response({'error': f'获取OCR模型列表失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AIImageRestoreView(APIView):
    """
    AI图像修复的API视图。
    接受一个图像文件并返回修复后的图像。
    """
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        """
        AI图像修复API
        参数: image (图像文件), model_name (模型名称), output_format (输出格式)
        """
        logger.info("AIImageRestoreView POST request received.")
        prediction_service = PredictionService()

        try:
            # 获取请求参数
            image_file = request.FILES.get('image')
            if not image_file:
                logger.warning("No image file provided for AI restoration.")
                return Response({
                    'error': '必须提供图像文件 (image)。'
                }, status=status.HTTP_400_BAD_REQUEST)

            output_format = request.POST.get('output_format', 'PNG').upper()
            model_name = request.POST.get('model_name', 'AI_Restorer_V1.0.1.7')

            logger.info(f"Processing image restoration: model='{model_name}', format='{output_format}'")

            # 使用PredictionService执行AI图像修复推理
            prediction_result = prediction_service.predict_image_restore(
                image_file=image_file,
                model_name=model_name,
                output_format=output_format
            )

            if prediction_result['status'] == 'success':
                logger.info("AI image restoration completed successfully")
                return Response(prediction_result, status=status.HTTP_200_OK)
            else:
                logger.error(f"AI image restoration failed: {prediction_result['message']}")
                return Response(prediction_result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except ValueError as e:
            # 参数验证错误
            logger.warning(f"Invalid parameters for AI image restoration: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"AI image restoration failed: {str(e)}", exc_info=True)
            return Response({
                'error': f'AI图像修复过程中发生意外错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def feature_matching_traditional_view(request):
    """
    传统特征点匹配API视图。
    接收模板图像、目标图像和模板ROI，返回匹配结果。
    """
    logger.info("feature_matching_traditional_view POST request received.")
    prediction_service = PredictionService()

    try:
        # 获取和验证参数
        template_image_file = request.FILES.get('template_image')
        target_image_file = request.FILES.get('target_image')
        template_roi_str = request.POST.get('template_roi')

        if not all([template_image_file, target_image_file, template_roi_str]):
            return Response({
                'error': '必须提供 template_image, target_image 和 template_roi 参数。'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 解析ROI参数
        try:
            template_roi = json.loads(template_roi_str)
            if not all(k in template_roi for k in ['x', 'y', 'width', 'height']):
                raise ValueError("ROI 必须包含 x, y, width, height。")
        except (json.JSONDecodeError, ValueError) as e:
            return Response({
                'error': f'无效的 template_roi 格式: {e}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取算法参数
        algorithm = request.POST.get('algorithm', 'SIFT')
        match_ratio_threshold = float(request.POST.get('match_ratio_threshold', 0.7))
        min_match_count = int(request.POST.get('min_match_count', 10))

        # 使用PredictionService执行传统特征匹配
        result = prediction_service.predict_feature_matching_traditional(
            template_image_file=template_image_file,
            target_image_file=target_image_file,
            template_roi=template_roi,
            algorithm=algorithm,
            match_ratio_threshold=match_ratio_threshold,
            min_match_count=min_match_count
        )

        # 返回结果
        response_status = status.HTTP_200_OK if result['status'] == 'success' else status.HTTP_400_BAD_REQUEST
        return Response(result, status=response_status)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for traditional feature matching: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Traditional feature matching failed: {str(e)}", exc_info=True)
        return Response({
            'error': f'处理请求时发生意外错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def list_example_images(request):
    """
    获取示例图片列表，数据源为 ExampleImage 模型。
    支持按分类和文件夹进行过滤。

    查询参数:
    - category: 'barcode', 'ocr', 'ai_restored' (必需)
    - folder: 文件夹路径，例如 'subfolder' 或 'invoices/2024' (可选)
    - source: 'dashboard' 时返回所有分类的图片数据

    返回格式:
    {
        "images": [
            { "id": 1, "name": "image1.jpg", "url": "...", ... },
            ...
        ],
        "folders": [ "subfolder1", "subfolder2" ]
    }
    """
    admin_service = AdminService()

    try:
        source = request.query_params.get('source')

        # 如果是仪表盘请求，返回所有分类的图片
        if source == 'dashboard':
            response_data = admin_service.get_dashboard_images_data()
            return Response(response_data, status=status.HTTP_200_OK)

        # 否则，执行现有的按分类和文件夹过滤的逻辑
        else:
            category = request.query_params.get('category')
            folder = request.query_params.get('folder', '').strip('/')

            if not category:
                return Response({
                    'error': '必须提供 category 查询参数。'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 使用AdminService处理查询逻辑
            response_data = admin_service.get_example_images_with_folders(category, folder)
            return Response(response_data, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for list_example_images: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error retrieving example images from database: {str(e)}", exc_info=True)
        return Response({
            'error': f'从数据库获取示例图片列表失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def serve_example_image(request, category, filename):
    """
    提供示例图片文件服务。

    参数:
    - category: 图片分类 (barcode, ocr, ai_restored)
    - filename: 图片文件名

    返回图片文件内容，支持浏览器缓存。
    """
    image_service = ImageService()

    try:
        # 使用ImageService处理图像请求
        response = image_service.handle_image_request(category, filename)

        logger.debug(f"Successfully served example image: {category}/{unquote(filename)}")
        return response

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for serve_example_image: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    except FileNotFoundError as e:
        # 文件不存在
        logger.warning(f"Example image not found: {category}/{filename}")
        return Response({'error': '图片文件未找到'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error serving example image {category}/{filename}: {str(e)}", exc_info=True)
        return Response({'error': f'提供图片文件服务失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# --- 管理员身份验证API ---

@api_view(['POST'])
def admin_login(request):
    """
    管理员登录API
    使用简单的密码验证机制
    """
    admin_service = AdminService()

    try:
        password = request.data.get('password')

        # 使用AdminService处理登录
        result = admin_service.admin_login(password, request.session)

        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 密码错误或为空
        logger.warning(f"Admin login failed: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_401_UNAUTHORIZED)
    except Exception as e:
        logger.error(f"Error during admin login: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'登录过程中发生错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def admin_logout(request):
    """
    管理员登出API
    清除会话中的管理员状态
    """
    admin_service = AdminService()

    try:
        # 使用AdminService处理登出
        result = admin_service.admin_logout(request.session)

        return Response(result, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error during admin logout: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'登出过程中发生错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def admin_check_status(request):
    """
    检查管理员登录状态API
    """
    admin_service = AdminService()

    try:
        # 使用AdminService检查状态
        result = admin_service.admin_check_status(request.session)

        return Response(result, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error checking admin status: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'检查状态时发生错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def upload_example_image(request):
    """
    管理员上传示例图片API
    参数: file, category, name(可选), description(可选)
    """
    admin_service = AdminService()

    try:
        # 获取请求参数
        image_file = request.FILES.get('file')
        category = request.POST.get('category')
        name = request.POST.get('name')
        description = request.POST.get('description')

        # 使用AdminService处理图片上传
        result = admin_service.upload_example_image(
            image_file=image_file,
            category=category,
            name=name,
            description=description
        )

        logger.info(f"Example image uploaded successfully: {category}/{result['name']}")
        return Response(result, status=status.HTTP_201_CREATED)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for upload_example_image: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error uploading example image: {str(e)}", exc_info=True)
        return Response({
            'error': f'上传图片失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def delete_example_images(request):
    """
    管理员批量删除示例图片API
    参数: ids (一个包含图片ID的数组)
    """
    admin_service = AdminService()

    try:
        data = request.data
        image_ids = data.get('ids', [])

        # 使用AdminService处理删除逻辑
        result = admin_service.delete_example_images(image_ids)

        # 构建兼容的响应格式
        response_data = {
            'success': result['success'],
            'message': result['message'],
            'deleted_count': result['deleted_count'],
            'failed_count': len(result['failed_ids']) + len(result['invalid_ids']),
            'failed_ids': result['failed_ids'],
            'not_found_ids': result['invalid_ids']  # 保持向后兼容
        }

        return Response(response_data, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for delete_example_images: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error processing delete_example_images request: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'删除图片过程中发生意外错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# --- 模型管理API ---

@api_view(['POST'])
@require_admin_auth
def delete_models(request):
    """
    管理员批量删除模型API
    参数: model_ids (数组)
    """
    admin_service = AdminService()

    try:
        data = request.data
        model_ids = data.get('model_ids', [])

        # 使用AdminService处理删除逻辑
        result = admin_service.delete_models(model_ids)

        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for delete_models: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error deleting models: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'删除模型失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@require_admin_auth
def update_model(request, model_id):
    """
    管理员更新模型信息API
    参数: model_id (路径参数), 模型信息 (请求体)
    """
    admin_service = AdminService()

    try:
        # 使用AdminService处理更新逻辑
        result = admin_service.update_model(model_id, request.data)

        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误或模型不存在
        logger.warning(f"Invalid parameters or model not found for update_model: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_model: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'更新模型失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def update_image_order(request):
    """
    更新图片的显示顺序和所属文件夹。
    如果提供了 `folder` 参数，则会将图片移动到该文件夹。
    """
    admin_service = AdminService()

    try:
        data = request.data
        category = data.get('category')
        target_folder = data.get('folder', '').strip('/')
        ordered_image_ids = data.get('ordered_image_ids', [])

        # 使用AdminService处理图片顺序更新
        result = admin_service.update_image_order(
            category=category,
            ordered_image_ids=ordered_image_ids,
            target_folder=target_folder
        )

        logger.info(f"Successfully updated image order: {result['updated_count']} images")
        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for update_image_order: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error updating image order/path: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@require_admin_auth
def update_example_image_description(request, image_id):
    """
    更新单个示例图片的描述信息。
    """
    admin_service = AdminService()

    try:
        data = request.data
        description = data.get('description', '')

        # 使用AdminService处理图片描述更新
        result = admin_service.update_example_image_description(
            image_id=int(image_id),
            description=description
        )

        logger.info(f"Successfully updated description for ExampleImage with ID {image_id}")
        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 图片不存在或参数错误
        logger.warning(f"Invalid parameters for update_example_image_description: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error updating example image description: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'更新图片描述失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
@api_view(['POST'])
@require_admin_auth
def create_example_folder(request):
    """
    为示例图片创建一个新的文件夹。
    这实际上是在文件系统上创建一个真实的目录。
    """
    admin_service = AdminService()

    try:
        data = request.data
        category = data.get('category')
        folder_name = data.get('folder_name', '').strip()

        # 使用AdminService处理文件夹创建
        result = admin_service.create_example_folder(
            category=category,
            folder_name=folder_name
        )

        logger.info(f"Successfully created folder: {category}/{folder_name}")
        return Response(result, status=status.HTTP_201_CREATED)

    except ValueError as e:
        # 参数验证错误或文件夹已存在
        logger.warning(f"Invalid parameters for create_example_folder: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error creating example folder: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'创建文件夹失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def rename_example_folder(request):
    """
    重命名一个文件夹。
    这包括重命名文件系统中的目录，并更新数据库中所有相关图片的path字段。
    """
    admin_service = AdminService()

    try:
        data = request.data
        category = data.get('category')
        old_name = data.get('old_name', '').strip()
        new_name = data.get('new_name', '').strip()

        # 使用AdminService处理文件夹重命名
        result = admin_service.rename_example_folder(
            category=category,
            old_name=old_name,
            new_name=new_name
        )

        logger.info(f"Successfully renamed folder: {old_name} -> {new_name}")
        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误
        logger.warning(f"Invalid parameters for rename_example_folder: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error renaming example folder: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'重命名文件夹失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def delete_example_folder(request):
    """
    删除一个空的文件夹。
    如果文件夹内有图片，则拒绝删除。
    """
    admin_service = AdminService()

    try:
        data = request.data
        category = data.get('category')
        folder_name = data.get('folder_name', '').strip()

        # 使用AdminService处理文件夹删除
        result = admin_service.delete_example_folder(
            category=category,
            folder_name=folder_name
        )

        logger.info(f"Successfully deleted folder: {category}/{folder_name}")
        return Response(result, status=status.HTTP_200_OK)

    except ValueError as e:
        # 参数验证错误或文件夹不为空
        logger.warning(f"Invalid parameters for delete_example_folder: {str(e)}")
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error deleting example folder: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'删除文件夹失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class FeatureMatchingModelView(APIView):
    """
    基于ONNX模型的特征点匹配API视图。
    """
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        """
        基于模型的特征匹配API
        参数: template_image, target_image, model_id, template_roi (可选), 以及各种预测器参数
        """
        logger.info("FeatureMatchingModelView POST request received.")
        prediction_service = PredictionService()

        try:
            # 获取和验证参数
            template_image_file = request.FILES.get('template_image')
            target_image_file = request.FILES.get('target_image')
            model_id = request.POST.get('model_id')
            template_roi_str = request.POST.get('template_roi')

            if not all([template_image_file, target_image_file, model_id]):
                return Response({
                    'error': '必须提供 template_image, target_image 和 model_id 参数。'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 解析ROI参数
            template_roi = None
            if template_roi_str:
                try:
                    template_roi = json.loads(template_roi_str)
                    if not all(k in template_roi for k in ['x', 'y', 'width', 'height']):
                        raise ValueError("ROI 必须包含 x, y, width, height。")
                except (json.JSONDecodeError, ValueError) as e:
                    return Response({
                        'error': f'无效的 template_roi 格式: {e}'
                    }, status=status.HTTP_400_BAD_REQUEST)

            # 解析预测器参数
            try:
                keypoints_count = int(request.POST.get('keypoints_count', 100))
                nms_grid_size = int(request.POST.get('nms_grid_size', 4))
                match_ratio_threshold = float(request.POST.get('match_ratio_threshold', 0.8))
                min_match_count = int(request.POST.get('min_match_count', 4))
                ransac_threshold = float(request.POST.get('ransac_threshold', 5.0))
            except (ValueError, TypeError) as e:
                return Response({
                    "status": "error",
                    "message": f"参数类型错误: {e}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 使用PredictionService执行基于模型的特征匹配
            result = prediction_service.predict_feature_matching_model(
                template_image_file=template_image_file,
                target_image_file=target_image_file,
                model_id=int(model_id),
                template_roi=template_roi,
                keypoints_count=keypoints_count,
                nms_grid_size=nms_grid_size,
                match_ratio_threshold=match_ratio_threshold,
                min_match_count=min_match_count,
                ransac_threshold=ransac_threshold
            )

            # 返回结果
            response_status = status.HTTP_200_OK if result.get('status') == 'success' else status.HTTP_400_BAD_REQUEST
            return Response(result, status=response_status)

        except ValueError as e:
            # 参数验证错误
            logger.warning(f"Invalid parameters for model-based feature matching: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Model-based feature matching failed: {str(e)}", exc_info=True)
            return Response({
                'error': f'处理请求时发生意外错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# --- Scanner Control APIs ---

from .services import scanner_service_instance

@api_view(['POST'])
def scanner_connect(request):
    """
    连接到扫描仪设备
    参数: ip_address
    """
    ip_address = request.data.get('ip_address')
    if not ip_address:
        return Response({'status': 'error', 'message': '必须提供ip_address'}, status=status.HTTP_400_BAD_REQUEST)
    
    result = scanner_service_instance.connect(ip_address)
    
    if result['status'] == 'success':
        return Response(result, status=status.HTTP_200_OK)
    else:
        return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def scanner_disconnect(request):
    """断开与扫描仪的连接"""
    result = scanner_service_instance.disconnect()
    return Response(result, status=status.HTTP_200_OK)

@api_view(['POST'])
def scanner_start_stream(request):
    """开始视频流"""
    result = scanner_service_instance.start_stream()
    
    if result['status'] == 'success':
        return Response(result, status=status.HTTP_200_OK)
    else:
        return Response(result, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def scanner_stop_stream(request):
    """停止视频流"""
    result = scanner_service_instance.stop_stream()
    return Response(result, status=status.HTTP_200_OK)

@api_view(['GET'])
def scanner_status(request):
    """获取扫描仪状态"""
    status_data = scanner_service_instance.get_status()
    return Response(status_data, status=status.HTTP_200_OK)
