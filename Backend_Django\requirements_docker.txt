albucore==0.0.13
albumentations==1.4.10
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
astor==0.8.1
beautifulsoup4==4.13.4
certifi==2025.4.26
charset-normalizer==3.4.2
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.3.2
cycler==0.12.1
Cython==3.1.0
decorator==5.2.1
Django==5.2.1
django-cors-headers==4.7.0
djangorestframework==3.15.1
# Django Channels for WebSocket support
channels==4.0.0
channels-redis==4.2.0
daphne==4.1.2
filelock==3.18.0
fire==0.7.0
flatbuffers==25.2.10
fonttools==4.57.0
fsspec==2025.3.2
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.31.1
humanfriendly==10.0
idna==3.10
imageio==2.37.0
imgaug==0.4.0
Jinja2==3.1.6
joblib==1.5.0
kiwisolver==1.4.8
lapx==0.5.11.post1
lazy_loader==0.4
lmdb==1.6.2
lxml==5.4.0
MarkupSafe==3.0.2
matplotlib==3.10.1
mpmath==1.3.0
networkx==3.4.2
numpy==1.26.4
onnxruntime==1.22.0
opencv-contrib-python==*********
opencv-python==*********
opencv-python-headless==*********
opt-einsum==3.3.0
packaging==25.0
paddleocr==2.10.0
paddlepaddle==3.0.0
pandas==2.2.3
pillow==11.2.1
protobuf==6.30.2
psutil==7.0.0
py-cpuinfo==9.0.0
pyclipper==1.3.0.post6
pydantic==2.11.4
pydantic_core==2.33.2
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-docx==1.1.2
pytz==2025.2
PyWavelets==1.8.0
PyYAML==6.0.2
RapidFuzz==3.13.0
requests==2.32.3
safetensors==0.5.3
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
setuptools==80.3.1
shapely==2.1.0
simsimd==6.2.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
sqlparse==0.5.3
stringzilla==3.12.5
sympy==1.14.0
termcolor==3.1.0
threadpoolctl==3.6.0
tifffile==2025.5.10
timm==1.0.15
tomli==2.2.1
torch==2.7.0
torchvision==0.22.0
tqdm==4.67.1
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
# Git依赖在Dockerfile中单独处理
# -e git+http://************:3000/Project_Deep_Learning/proj_ai_roi_det.git@557cf7f70159037ab0d3a7883b96ce4874b63d5b#egg=ultralytics
ultralytics-thop==2.0.14
urllib3==2.4.0
