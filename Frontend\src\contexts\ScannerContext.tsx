import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { scannerConnect, scannerDisconnect, scannerStartStream, scannerStopStream } from '../services/scannerApi';
import { InputSource } from '../components/InputSourceSwitcher';

// --- Types ---
interface ScannerState {
  isConnected: boolean;
  isStreaming: boolean;
  deviceIP: string | null;
  frameData: string | null; // Base64 encoded image
  inputSource: InputSource;
}

interface ScannerContextType extends ScannerState {
  connect: (ip: string) => Promise<boolean>;
  disconnect: () => Promise<void>;
  startStream: () => Promise<boolean>;
  stopStream: () => Promise<void>;
}

// --- Context Definition ---
const ScannerContext = createContext<ScannerContextType | undefined>(undefined);

// --- Provider Component ---
export const ScannerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<ScannerState>({
    isConnected: false,
    isStreaming: false,
    deviceIP: null,
    frameData: null,
    inputSource: 'file', // Default to file input
  });

  const socketRef = useRef<WebSocket | null>(null);

  const setupSocket = useCallback(() => {
    // Disconnect previous socket if exists
    if (socketRef.current) {
      socketRef.current.close();
    }

    const newSocket = new WebSocket('ws://localhost:8888/ws/scanner/');

    newSocket.onopen = () => {
      console.log('Scanner WebSocket connected');
    };

    newSocket.onclose = () => {
      console.log('Scanner WebSocket disconnected');
      setState(prev => ({ ...prev, isStreaming: false }));
    };

    newSocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.image) {
        setState(prev => ({ ...prev, frameData: `data:image/jpeg;base64,${data.image}` }));
      }
    };

    socketRef.current = newSocket;
  }, []);

  const connect = useCallback(async (ip: string): Promise<boolean> => {
    try {
      const response = await scannerConnect(ip);
      if (response.status === 'success') {
        setState(prev => ({ ...prev, isConnected: true, deviceIP: ip, inputSource: 'scanner' })); // Switch to scanner on connect
        setupSocket();
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to connect scanner:", error);
      return false;
    }
  }, [setupSocket]);

  const disconnect = useCallback(async () => {
    try {
      await scannerDisconnect();
      if (socketRef.current) {
        socketRef.current.close();
        socketRef.current = null;
      }
      setState({
        isConnected: false,
        isStreaming: false,
        deviceIP: null,
        frameData: null,
        inputSource: 'file', // Reset to default
      });
    } catch (error) {
      console.error("Failed to disconnect scanner:", error);
    }
  }, []);

  const startStream = useCallback(async (): Promise<boolean> => {
    if (!state.isConnected) return false;
    try {
      const response = await scannerStartStream();
      if (response.status === 'success') {
        setState(prev => ({ ...prev, isStreaming: true }));
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to start stream:", error);
      return false;
    }
  }, [state.isConnected]);

  const stopStream = useCallback(async () => {
    if (!state.isStreaming) return;
    try {
      await scannerStopStream();
      setState(prev => ({ ...prev, isStreaming: false }));
    } catch (error) {
      console.error("Failed to stop stream:", error);
    }
  }, [state.isStreaming]);

  return (
    <ScannerContext.Provider value={{ ...state, connect, disconnect, startStream, stopStream }}>
      {children}
    </ScannerContext.Provider>
  );
};

// --- Custom Hook ---
export const useScanner = (): ScannerContextType => {
  const context = useContext(ScannerContext);
  if (context === undefined) {
    throw new Error('useScanner must be used within a ScannerProvider');
  }
  return context;
};