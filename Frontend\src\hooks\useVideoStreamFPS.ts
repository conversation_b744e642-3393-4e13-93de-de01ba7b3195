import { useRef, useEffect, useState, useCallback } from 'react';

interface VideoStreamFPSStats {
  // WebSocket相关
  wsMessageFPS: number; // WebSocket消息接收FPS
  wsMessageCount: number; // WebSocket消息总数
  wsLastMessageTime: number; // 最后一次消息时间
  
  // 图像更新相关
  imageUpdateFPS: number; // 图像更新FPS
  imageUpdateCount: number; // 图像更新总数
  imageLastUpdateTime: number; // 最后一次图像更新时间
  
  // 渲染相关
  renderFPS: number; // 实际渲染FPS
  renderCount: number; // 渲染总数
  renderLastTime: number; // 最后一次渲染时间
  
  // 性能指标
  avgLatency: number; // 平均延迟(ms)
  maxLatency: number; // 最大延迟(ms)
  minLatency: number; // 最小延迟(ms)
  
  // 质量指标
  duplicateFrames: number; // 重复帧数
  skippedFrames: number; // 跳过帧数
}

interface VideoStreamFPSOptions {
  updateInterval?: number;
  maxSamples?: number;
  enabled?: boolean;
}

export const useVideoStreamFPS = (
  frameData: string | null,
  isStreaming: boolean,
  options: VideoStreamFPSOptions = {}
) => {
  const {
    updateInterval = 1000,
    maxSamples = 60,
    enabled = true
  } = options;

  const [stats, setStats] = useState<VideoStreamFPSStats>({
    wsMessageFPS: 0,
    wsMessageCount: 0,
    wsLastMessageTime: 0,
    imageUpdateFPS: 0,
    imageUpdateCount: 0,
    imageLastUpdateTime: 0,
    renderFPS: 0,
    renderCount: 0,
    renderLastTime: 0,
    avgLatency: 0,
    maxLatency: 0,
    minLatency: 0,
    duplicateFrames: 0,
    skippedFrames: 0
  });

  // 数据存储
  const wsMessageTimesRef = useRef<number[]>([]);
  const imageUpdateTimesRef = useRef<number[]>([]);
  const renderTimesRef = useRef<number[]>([]);
  const latenciesRef = useRef<number[]>([]);
  
  // 计数器
  const countersRef = useRef({
    wsMessages: 0,
    imageUpdates: 0,
    renders: 0,
    duplicateFrames: 0,
    skippedFrames: 0
  });

  // 上一帧数据
  const lastFrameDataRef = useRef<string | null>(null);
  const lastUpdateTimeRef = useRef<number>(0);
  const animationFrameRef = useRef<number>();

  // 记录WebSocket消息
  const recordWebSocketMessage = useCallback(() => {
    if (!enabled || !isStreaming) return;

    const now = performance.now();
    wsMessageTimesRef.current.push(now);
    countersRef.current.wsMessages++;

    if (wsMessageTimesRef.current.length > maxSamples) {
      wsMessageTimesRef.current.shift();
    }
  }, [enabled, isStreaming, maxSamples]);

  // 记录图像更新
  const recordImageUpdate = useCallback(() => {
    if (!enabled || !isStreaming) return;

    const now = performance.now();
    imageUpdateTimesRef.current.push(now);
    countersRef.current.imageUpdates++;

    // 检测重复帧
    if (lastFrameDataRef.current === frameData && frameData !== null) {
      countersRef.current.duplicateFrames++;
    }

    // 计算延迟 (假设图像数据包含时间戳信息)
    if (frameData && lastFrameDataRef.current !== frameData) {
      // 这里可以根据实际情况计算延迟
      // 目前使用简单的时间差作为延迟指标
      if (lastUpdateTimeRef.current > 0) {
        const latency = now - lastUpdateTimeRef.current;
        latenciesRef.current.push(latency);
        
        if (latenciesRef.current.length > maxSamples) {
          latenciesRef.current.shift();
        }
      }
      lastUpdateTimeRef.current = now;
    }

    lastFrameDataRef.current = frameData;

    if (imageUpdateTimesRef.current.length > maxSamples) {
      imageUpdateTimesRef.current.shift();
    }
  }, [enabled, isStreaming, frameData, maxSamples]);

  // 记录渲染
  const recordRender = useCallback(() => {
    if (!enabled || !isStreaming) return;

    const now = performance.now();
    renderTimesRef.current.push(now);
    countersRef.current.renders++;

    if (renderTimesRef.current.length > maxSamples) {
      renderTimesRef.current.shift();
    }

    // 继续监控渲染
    animationFrameRef.current = requestAnimationFrame(recordRender);
  }, [enabled, isStreaming, maxSamples]);

  // 计算FPS
  const calculateFPS = useCallback((times: number[]) => {
    if (times.length < 2) return 0;
    
    const timeSpan = times[times.length - 1] - times[0];
    const frameCount = times.length - 1;
    
    return frameCount > 0 ? (frameCount * 1000) / timeSpan : 0;
  }, []);

  // 更新统计数据
  const updateStats = useCallback(() => {
    const wsMessageFPS = calculateFPS(wsMessageTimesRef.current);
    const imageUpdateFPS = calculateFPS(imageUpdateTimesRef.current);
    const renderFPS = calculateFPS(renderTimesRef.current);

    // 计算延迟统计
    const latencies = latenciesRef.current;
    const avgLatency = latencies.length > 0 
      ? latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length 
      : 0;
    const maxLatency = latencies.length > 0 ? Math.max(...latencies) : 0;
    const minLatency = latencies.length > 0 ? Math.min(...latencies) : 0;

    setStats({
      wsMessageFPS: Math.round(wsMessageFPS * 100) / 100,
      wsMessageCount: countersRef.current.wsMessages,
      wsLastMessageTime: wsMessageTimesRef.current[wsMessageTimesRef.current.length - 1] || 0,
      
      imageUpdateFPS: Math.round(imageUpdateFPS * 100) / 100,
      imageUpdateCount: countersRef.current.imageUpdates,
      imageLastUpdateTime: imageUpdateTimesRef.current[imageUpdateTimesRef.current.length - 1] || 0,
      
      renderFPS: Math.round(renderFPS * 100) / 100,
      renderCount: countersRef.current.renders,
      renderLastTime: renderTimesRef.current[renderTimesRef.current.length - 1] || 0,
      
      avgLatency: Math.round(avgLatency * 100) / 100,
      maxLatency: Math.round(maxLatency * 100) / 100,
      minLatency: Math.round(minLatency * 100) / 100,
      
      duplicateFrames: countersRef.current.duplicateFrames,
      skippedFrames: countersRef.current.skippedFrames
    });
  }, [calculateFPS]);

  // 重置统计
  const resetStats = useCallback(() => {
    wsMessageTimesRef.current = [];
    imageUpdateTimesRef.current = [];
    renderTimesRef.current = [];
    latenciesRef.current = [];
    
    countersRef.current = {
      wsMessages: 0,
      imageUpdates: 0,
      renders: 0,
      duplicateFrames: 0,
      skippedFrames: 0
    };

    lastFrameDataRef.current = null;
    lastUpdateTimeRef.current = 0;

    setStats({
      wsMessageFPS: 0,
      wsMessageCount: 0,
      wsLastMessageTime: 0,
      imageUpdateFPS: 0,
      imageUpdateCount: 0,
      imageLastUpdateTime: 0,
      renderFPS: 0,
      renderCount: 0,
      renderLastTime: 0,
      avgLatency: 0,
      maxLatency: 0,
      minLatency: 0,
      duplicateFrames: 0,
      skippedFrames: 0
    });
  }, []);

  // 监听frameData变化
  useEffect(() => {
    if (frameData !== lastFrameDataRef.current) {
      recordWebSocketMessage();
      recordImageUpdate();
    }
  }, [frameData, recordWebSocketMessage, recordImageUpdate]);

  // 启动渲染监控
  useEffect(() => {
    if (enabled && isStreaming) {
      animationFrameRef.current = requestAnimationFrame(recordRender);
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enabled, isStreaming, recordRender]);

  // 定期更新统计
  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(updateStats, updateInterval);
    return () => clearInterval(interval);
  }, [enabled, updateInterval, updateStats]);

  return {
    stats,
    resetStats,
    isMonitoring: enabled && isStreaming
  };
};
