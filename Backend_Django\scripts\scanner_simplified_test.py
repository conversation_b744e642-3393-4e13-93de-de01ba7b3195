import os
import sys
import time

# -- 配置Django环境 --
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_project.settings')
import django
django.setup()
# -- Django环境配置结束 --

from vision_app.services import scanner_service_instance

# --- 配置 ---
DEVICE_IP = "************"  # 修改为你的设备IP地址

def test_simplified_scanner():
    """测试简化后的扫描仪实现"""
    print(f"测试简化后的扫描仪实现...")
    print(f"设备IP: {DEVICE_IP}")
    print("-" * 50)
    
    try:
        # 1. 连接设备
        print("1. 连接设备...")
        result = scanner_service_instance.connect(DEVICE_IP)
        print(f"   结果: {result['status']} - {result['message']}")
        
        if result['status'] != 'success':
            print("连接失败，测试终止")
            return
        
        # 2. 启动视频流
        print("\n2. 启动视频流...")
        result = scanner_service_instance.start_stream()
        print(f"   结果: {result['status']} - {result['message']}")
        
        if result['status'] != 'success':
            print("启动视频流失败")
            return
        
        # 3. 监控性能 - 持续10秒
        print("\n3. 监控性能（10秒）...")
        start_time = time.time()
        last_total_frames = 0

        while time.time() - start_time < 10:
            time.sleep(1)
            status = scanner_service_instance.get_status()
            current_total_frames = status.get('total_frames_processed', 0)
            queue_size = status.get('image_queue_size', 0)
            has_inference = status.get('has_inference_frame', False)

            # 计算每秒处理的帧数
            frames_this_second = current_total_frames - last_total_frames
            last_total_frames = current_total_frames

            elapsed = time.time() - start_time
            print(f"   第{elapsed:.0f}秒: 处理FPS={frames_this_second}, "
                  f"队列大小={queue_size}, "
                  f"推理帧={'有' if has_inference else '无'}, "
                  f"总帧数={current_total_frames}")
        
        # 4. 获取推理帧测试
        print("\n4. 测试推理帧获取...")
        for i in range(3):
            inference_frame = scanner_service_instance.get_latest_inference_frame()
            if inference_frame:
                print(f"   第{i+1}次: 获取到推理帧 {inference_frame['width']}x{inference_frame['height']}, "
                      f"大小={len(inference_frame['data'])}字节, "
                      f"时间戳={inference_frame['timestamp']:.3f}")
            else:
                print(f"   第{i+1}次: 未获取到推理帧")
            time.sleep(0.5)
        
        # 5. 停止视频流
        print("\n5. 停止视频流...")
        result = scanner_service_instance.stop_stream()
        print(f"   结果: {result['status']} - {result['message']}")
        
        # 6. 断开连接
        print("\n6. 断开连接...")
        result = scanner_service_instance.disconnect()
        print(f"   结果: {result['status']} - {result['message']}")
        
        print("\n✅ 简化版本测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保清理资源
        try:
            scanner_service_instance.disconnect()
        except:
            pass

if __name__ == "__main__":
    test_simplified_scanner()
