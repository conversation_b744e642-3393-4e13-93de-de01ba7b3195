import threading
import queue
from typing import Optional, Dict, Any
from django.conf import settings
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from .base import BaseService
from ..scanner.sdk import MdScanner
from ..scanner.image_utils import nv12_to_jpeg

class ScannerService(BaseService):
    """
    扫描仪管理服务 (单例)
    
    负责管理与硬件扫描仪的连接、控制和数据流。
    """
    
    def __init__(self):
        super().__init__()
        self.scanner: Optional[MdScanner] = None
        self.is_streaming = False
        self.device_ip: Optional[str] = None
        self.image_queue = queue.Queue(maxsize=10)
        self.processing_thread: Optional[threading.Thread] = None
        self.channel_layer = get_channel_layer()

    def connect(self, ip_address: str) -> Dict[str, Any]:
        """连接到扫描仪设备"""
        if self.scanner and self.scanner.running:
            if self.device_ip == ip_address:
                return {'status': 'success', 'message': f'已连接到设备 {ip_address}'}
            else:
                self.disconnect()

        self.log_info(f"正在连接到设备: {ip_address}...")
        try:
            self.scanner = MdScanner(ip_address)
            self.device_ip = ip_address
            self.log_info(f"成功连接到设备: {ip_address}")
            return {'status': 'success', 'message': f'成功连接到设备 {ip_address}'}
        except Exception as e:
            self.log_error(f"连接设备 {ip_address} 失败: {e}")
            self.scanner = None
            self.device_ip = None
            return {'status': 'error', 'message': f'连接设备失败: {e}'}

    def disconnect(self) -> Dict[str, Any]:
        """断开与扫描仪的连接"""
        if self.scanner and self.scanner.running:
            self.log_info(f"正在断开与设备 {self.device_ip} 的连接...")
            if self.is_streaming:
                self.stop_stream()
            
            self.scanner.__exit__(None, None, None)
            self.scanner = None
            self.device_ip = None
            self.log_info("设备连接已断开")
            return {'status': 'success', 'message': '设备连接已断开'}
        return {'status': 'success', 'message': '设备未连接'}

    def start_stream(self) -> Dict[str, Any]:
        """开始视频流"""
        if not self.scanner or not self.scanner.running:
            return {'status': 'error', 'message': '设备未连接'}
        if self.is_streaming:
            return {'status': 'error', 'message': '视频流已在运行'}

        self.log_info("正在启动扫描仪...")
        start_event = threading.Event()
        start_result = -1

        def _start_callback(ret):
            nonlocal start_result
            start_result = ret
            start_event.set()

        self.scanner.MdScanner_Start(_start_callback)
        if not start_event.wait(timeout=5):
            self.log_warning("启动扫描仪超时")
            return {'status': 'error', 'message': '启动扫描仪超时'}

        if start_result != 0:
            self.log_error(f"启动扫描仪失败，返回码: {start_result}")
            return {'status': 'error', 'message': f'启动扫描仪失败，返回码: {start_result}'}

        self.log_info("扫描仪启动成功，开始捕获图像...")
        self.is_streaming = True
        self.scanner.start_continuous_capture(self._image_callback)
        
        self.processing_thread = threading.Thread(target=self._process_image_queue)
        self.processing_thread.daemon = True
        self.processing_thread.start()

        return {'status': 'success', 'message': '视频流已启动'}

    def stop_stream(self) -> Dict[str, Any]:
        """停止视频流"""
        if not self.is_streaming:
            return {'status': 'success', 'message': '视频流未运行'}

        self.log_info("正在停止视频流...")
        self.is_streaming = False
        if self.scanner:
            self.scanner.stop_continuous_capture()

        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2)

        self.log_info("视频流已停止")
        return {'status': 'success', 'message': '视频流已停止'}

    def _image_callback(self, width, height, format, size, out, ret):
        """硬件SDK的图像回调函数"""
        if not self.is_streaming or ret != 0 or size == 0:
            return

        if format == 0:  # NV12
            try:
                # 将原始数据放入队列，避免在回调中做耗时操作
                self.image_queue.put_nowait((bytes(out), width, height))
            except queue.Full:
                # 队列满时丢弃最旧的帧
                try:
                    self.image_queue.get_nowait()
                    self.image_queue.put_nowait((bytes(out), width, height))
                except queue.Empty:
                    pass

    def _process_image_queue(self):
        """处理图像队列的后台线程"""
        self.log_info("图像处理线程已启动")
        while self.is_streaming:
            try:
                raw_data, width, height = self.image_queue.get(timeout=1)
                
                jpeg_io = nv12_to_jpeg(raw_data, width, height)
                if jpeg_io:
                    jpeg_bytes = jpeg_io.getvalue()
                    # 通过Channel Layer发送数据
                    async_to_sync(self.channel_layer.group_send)(
                        "scanner_stream",
                        {
                            "type": "send_image_data",
                            "image_bytes": jpeg_bytes,
                        }
                    )
            except queue.Empty:
                continue
            except Exception as e:
                self.log_error(f"处理图像队列时发生错误: {e}")
        self.log_info("图像处理线程已停止")

    def get_status(self) -> Dict[str, Any]:
        """获取扫描仪当前状态"""
        return {
            'is_connected': self.scanner is not None and self.scanner.running,
            'is_streaming': self.is_streaming,
            'device_ip': self.device_ip
        }