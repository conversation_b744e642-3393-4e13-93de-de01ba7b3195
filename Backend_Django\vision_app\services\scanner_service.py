import threading
import queue
import time
from typing import Optional, Dict, Any, Callable
from django.conf import settings
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from .base import BaseService
from ..scanner.sdk import MdScanner
from ..scanner.image_utils import nv12_to_jpeg

class ScannerService(BaseService):
    """
    扫描仪管理服务 (单例)

    负责管理与硬件扫描仪的连接、控制和数据流。
    支持双流架构：
    1. 显示流 - 低质量、高帧率，用于实时预览
    2. 推理流 - 高质量、按需获取，用于AI推理
    """

    def __init__(self):
        super().__init__()
        self.scanner: Optional[MdScanner] = None
        self.is_streaming = False
        self.device_ip: Optional[str] = None

        # 显示流相关
        self.display_queue = queue.Queue(maxsize=5)  # 较小队列，保持低延迟
        self.display_thread: Optional[threading.Thread] = None
        self.channel_layer = get_channel_layer()

        # 推理流相关
        self.inference_queue = queue.Queue(maxsize=3)  # 保存最新的高质量帧
        self.latest_inference_frame = None  # 最新的推理用帧
        self.inference_frame_lock = threading.Lock()
        self.inference_thread: Optional[threading.Thread] = None

        # 帧率控制
        self.last_display_time = 0
        self.display_interval = 1.0 / 5.0  # 目标5FPS显示
        self.frame_skip_counter = 0

    def connect(self, ip_address: str) -> Dict[str, Any]:
        """连接到扫描仪设备"""
        if self.scanner and self.scanner.running:
            if self.device_ip == ip_address:
                return {'status': 'success', 'message': f'已连接到设备 {ip_address}'}
            else:
                self.disconnect()

        self.log_info(f"正在连接到设备: {ip_address}...")
        try:
            self.scanner = MdScanner(ip_address)
            self.device_ip = ip_address
            self.log_info(f"成功连接到设备: {ip_address}")
            return {'status': 'success', 'message': f'成功连接到设备 {ip_address}'}
        except Exception as e:
            self.log_error(f"连接设备 {ip_address} 失败: {e}")
            self.scanner = None
            self.device_ip = None
            return {'status': 'error', 'message': f'连接设备失败: {e}'}

    def disconnect(self) -> Dict[str, Any]:
        """断开与扫描仪的连接"""
        if self.scanner and self.scanner.running:
            self.log_info(f"正在断开与设备 {self.device_ip} 的连接...")
            if self.is_streaming:
                self.stop_stream()
            
            self.scanner.__exit__(None, None, None)
            self.scanner = None
            self.device_ip = None
            self.log_info("设备连接已断开")
            return {'status': 'success', 'message': '设备连接已断开'}
        return {'status': 'success', 'message': '设备未连接'}

    def start_stream(self) -> Dict[str, Any]:
        """开始视频流"""
        if not self.scanner or not self.scanner.running:
            return {'status': 'error', 'message': '设备未连接'}
        if self.is_streaming:
            return {'status': 'error', 'message': '视频流已在运行'}

        self.log_info("正在启动扫描仪...")
        start_event = threading.Event()
        start_result = -1

        def _start_callback(ret):
            nonlocal start_result
            start_result = ret
            start_event.set()

        self.scanner.MdScanner_Start(_start_callback)
        if not start_event.wait(timeout=5):
            self.log_warning("启动扫描仪超时")
            return {'status': 'error', 'message': '启动扫描仪超时'}

        if start_result != 0:
            self.log_error(f"启动扫描仪失败，返回码: {start_result}")
            return {'status': 'error', 'message': f'启动扫描仪失败，返回码: {start_result}'}

        self.log_info("扫描仪启动成功，开始捕获图像...")
        self.is_streaming = True
        self.scanner.start_continuous_capture(self._image_callback)

        # 启动显示流处理线程
        self.display_thread = threading.Thread(target=self._process_display_queue)
        self.display_thread.daemon = True
        self.display_thread.start()

        # 启动推理流处理线程
        self.inference_thread = threading.Thread(target=self._process_inference_queue)
        self.inference_thread.daemon = True
        self.inference_thread.start()

        return {'status': 'success', 'message': '视频流已启动'}

    def stop_stream(self) -> Dict[str, Any]:
        """停止视频流"""
        if not self.is_streaming:
            return {'status': 'success', 'message': '视频流未运行'}

        self.log_info("正在停止视频流...")
        self.is_streaming = False
        if self.scanner:
            self.scanner.stop_continuous_capture()

        # 等待显示线程结束
        if self.display_thread and self.display_thread.is_alive():
            self.display_thread.join(timeout=2)

        # 等待推理线程结束
        if self.inference_thread and self.inference_thread.is_alive():
            self.inference_thread.join(timeout=2)

        self.log_info("视频流已停止")
        return {'status': 'success', 'message': '视频流已停止'}

    def _image_callback(self, width, height, format, size, out, ret):
        """
        硬件SDK的图像回调函数 - 智能分流
        根据帧率控制策略，将图像分发到显示流和推理流
        """
        if not self.is_streaming or ret != 0 or size == 0:
            return

        if format == 0:  # NV12
            current_time = time.time()
            raw_data = bytes(out)

            # 推理流：总是保存最新的高质量帧
            try:
                self.inference_queue.put_nowait((raw_data, width, height))
            except queue.Full:
                # 队列满时丢弃最旧的帧，保留最新的
                try:
                    self.inference_queue.get_nowait()
                    self.inference_queue.put_nowait((raw_data, width, height))
                except queue.Empty:
                    pass

            # 显示流：帧率控制，只有达到间隔时间才处理
            if current_time - self.last_display_time >= self.display_interval:
                try:
                    self.display_queue.put_nowait((raw_data, width, height))
                    self.last_display_time = current_time
                    self.frame_skip_counter = 0
                except queue.Full:
                    # 显示队列满时丢弃最旧的帧
                    try:
                        self.display_queue.get_nowait()
                        self.display_queue.put_nowait((raw_data, width, height))
                        self.last_display_time = current_time
                    except queue.Empty:
                        pass
            else:
                self.frame_skip_counter += 1

    def _process_display_queue(self):
        """处理显示流队列的后台线程 - 低质量高帧率"""
        self.log_info("显示流处理线程已启动")
        while self.is_streaming:
            try:
                raw_data, width, height = self.display_queue.get(timeout=1)

                # 使用较低的JPEG质量以提高处理速度
                jpeg_io = nv12_to_jpeg(raw_data, width, height, quality=60)
                if jpeg_io:
                    jpeg_bytes = jpeg_io.getvalue()
                    # 通过Channel Layer发送数据
                    async_to_sync(self.channel_layer.group_send)(
                        "scanner_stream",
                        {
                            "type": "send_image_data",
                            "image_bytes": jpeg_bytes,
                        }
                    )
            except queue.Empty:
                continue
            except Exception as e:
                self.log_error(f"处理显示队列时发生错误: {e}")
        self.log_info("显示流处理线程已停止")

    def _process_inference_queue(self):
        """处理推理流队列的后台线程 - 高质量按需获取"""
        self.log_info("推理流处理线程已启动")
        while self.is_streaming:
            try:
                raw_data, width, height = self.inference_queue.get(timeout=1)

                # 使用高质量JPEG编码用于推理
                jpeg_io = nv12_to_jpeg(raw_data, width, height, quality=95)
                if jpeg_io:
                    # 更新最新的推理帧
                    with self.inference_frame_lock:
                        self.latest_inference_frame = {
                            'data': jpeg_io.getvalue(),
                            'width': width,
                            'height': height,
                            'timestamp': time.time()
                        }
            except queue.Empty:
                continue
            except Exception as e:
                self.log_error(f"处理推理队列时发生错误: {e}")
        self.log_info("推理流处理线程已停止")

    def get_latest_inference_frame(self) -> Optional[Dict[str, Any]]:
        """
        获取最新的推理用高质量帧
        返回: {
            'data': JPEG字节数据,
            'width': 图像宽度,
            'height': 图像高度,
            'timestamp': 时间戳
        }
        """
        with self.inference_frame_lock:
            if self.latest_inference_frame:
                return self.latest_inference_frame.copy()
            return None

    def get_status(self) -> Dict[str, Any]:
        """获取扫描仪当前状态"""
        status = {
            'is_connected': self.scanner is not None and self.scanner.running,
            'is_streaming': self.is_streaming,
            'device_ip': self.device_ip
        }

        # 添加性能统计信息
        if self.is_streaming:
            status.update({
                'display_queue_size': self.display_queue.qsize(),
                'inference_queue_size': self.inference_queue.qsize(),
                'frames_skipped': self.frame_skip_counter,
                'has_inference_frame': self.latest_inference_frame is not None
            })

        return status