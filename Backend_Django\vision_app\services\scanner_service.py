import threading
import queue
import time
from typing import Optional, Dict, Any, Tuple
from django.conf import settings
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from .base import BaseService
from ..scanner.sdk import MdScanner
from ..scanner.image_utils import nv12_to_jpeg


class FrameManager:
    """
    智能帧管理器 - 优化实时性能

    功能：
    1. 维护最新的高质量原始帧用于AI推理
    2. 快速生成低质量帧用于显示
    3. 自动丢弃过时帧，避免延迟积累
    """

    def __init__(self, max_display_queue_size=3, max_inference_queue_size=2):
        self.max_display_queue_size = max_display_queue_size
        self.max_inference_queue_size = max_inference_queue_size

        # 显示队列 - 存储快速编码的低质量JPEG
        self.display_queue = queue.Queue(maxsize=max_display_queue_size)

        # 推理队列 - 存储原始NV12数据用于高质量处理
        self.inference_queue = queue.Queue(maxsize=max_inference_queue_size)

        # 最新帧信息
        self.latest_frame_info = None
        self.latest_frame_lock = threading.Lock()

        # 性能统计
        self.frames_received = 0
        self.frames_dropped = 0
        self.last_stats_time = time.time()

    def add_frame(self, raw_data: bytes, width: int, height: int) -> bool:
        """
        添加新帧到管理器
        :return: True if frame was processed, False if dropped
        """
        current_time = time.time()
        frame_info = {
            'data': raw_data,
            'width': width,
            'height': height,
            'timestamp': current_time
        }

        self.frames_received += 1

        # 更新最新帧信息（用于推理）
        with self.latest_frame_lock:
            self.latest_frame_info = frame_info

        # 尝试添加到推理队列（非阻塞）
        try:
            self.inference_queue.put_nowait(frame_info.copy())
        except queue.Full:
            # 推理队列满时，移除最旧的帧
            try:
                self.inference_queue.get_nowait()
                self.inference_queue.put_nowait(frame_info.copy())
            except queue.Empty:
                pass

        # 生成快速显示帧
        display_jpeg = nv12_to_jpeg(raw_data, width, height, quality=60, fast_mode=True)
        if display_jpeg:
            try:
                self.display_queue.put_nowait({
                    'jpeg_data': display_jpeg.getvalue(),
                    'timestamp': current_time
                })
            except queue.Full:
                # 显示队列满时，移除最旧的帧
                try:
                    self.display_queue.get_nowait()
                    self.display_queue.put_nowait({
                        'jpeg_data': display_jpeg.getvalue(),
                        'timestamp': current_time
                    })
                    self.frames_dropped += 1
                except queue.Empty:
                    pass

        return True

    def get_display_frame(self, timeout=0.1):
        """获取用于显示的快速编码帧"""
        try:
            return self.display_queue.get(timeout=timeout)
        except queue.Empty:
            return None

    def get_latest_inference_frame(self):
        """获取最新的高质量推理帧"""
        with self.latest_frame_lock:
            if self.latest_frame_info:
                # 生成高质量JPEG用于推理
                frame_info = self.latest_frame_info.copy()
                high_quality_jpeg = nv12_to_jpeg(
                    frame_info['data'],
                    frame_info['width'],
                    frame_info['height'],
                    quality=95,
                    fast_mode=False
                )
                if high_quality_jpeg:
                    return {
                        'jpeg_data': high_quality_jpeg.getvalue(),
                        'width': frame_info['width'],
                        'height': frame_info['height'],
                        'timestamp': frame_info['timestamp']
                    }
        return None

    def get_stats(self):
        """获取性能统计"""
        current_time = time.time()
        elapsed = current_time - self.last_stats_time
        if elapsed > 0:
            fps = self.frames_received / elapsed
            drop_rate = self.frames_dropped / max(1, self.frames_received) * 100
            return {
                'fps': fps,
                'frames_received': self.frames_received,
                'frames_dropped': self.frames_dropped,
                'drop_rate': drop_rate,
                'display_queue_size': self.display_queue.qsize(),
                'inference_queue_size': self.inference_queue.qsize()
            }
        return None

    def reset_stats(self):
        """重置统计信息"""
        self.frames_received = 0
        self.frames_dropped = 0
        self.last_stats_time = time.time()

class ScannerService(BaseService):
    """
    扫描仪管理服务 (单例) - 优化版本

    负责管理与硬件扫描仪的连接、控制和数据流。
    使用智能帧管理器提升实时性能。
    """

    def __init__(self):
        super().__init__()
        self.scanner: Optional[MdScanner] = None
        self.is_streaming = False
        self.device_ip: Optional[str] = None

        # 使用新的帧管理器替代简单队列
        self.frame_manager = FrameManager()

        # 处理线程
        self.display_processing_thread: Optional[threading.Thread] = None
        self.channel_layer = get_channel_layer()

    def connect(self, ip_address: str) -> Dict[str, Any]:
        """连接到扫描仪设备"""
        if self.scanner and self.scanner.running:
            if self.device_ip == ip_address:
                return {'status': 'success', 'message': f'已连接到设备 {ip_address}'}
            else:
                self.disconnect()

        self.log_info(f"正在连接到设备: {ip_address}...")
        try:
            self.scanner = MdScanner(ip_address)
            self.device_ip = ip_address
            self.log_info(f"成功连接到设备: {ip_address}")
            return {'status': 'success', 'message': f'成功连接到设备 {ip_address}'}
        except Exception as e:
            self.log_error(f"连接设备 {ip_address} 失败: {e}")
            self.scanner = None
            self.device_ip = None
            return {'status': 'error', 'message': f'连接设备失败: {e}'}

    def disconnect(self) -> Dict[str, Any]:
        """断开与扫描仪的连接"""
        if self.scanner and self.scanner.running:
            self.log_info(f"正在断开与设备 {self.device_ip} 的连接...")
            if self.is_streaming:
                self.stop_stream()
            
            self.scanner.__exit__(None, None, None)
            self.scanner = None
            self.device_ip = None
            self.log_info("设备连接已断开")
            return {'status': 'success', 'message': '设备连接已断开'}
        return {'status': 'success', 'message': '设备未连接'}

    def start_stream(self) -> Dict[str, Any]:
        """开始视频流"""
        if not self.scanner or not self.scanner.running:
            return {'status': 'error', 'message': '设备未连接'}
        if self.is_streaming:
            return {'status': 'error', 'message': '视频流已在运行'}

        self.log_info("正在启动扫描仪...")
        start_event = threading.Event()
        start_result = -1

        def _start_callback(ret):
            nonlocal start_result
            start_result = ret
            start_event.set()

        self.scanner.MdScanner_Start(_start_callback)
        if not start_event.wait(timeout=5):
            self.log_warning("启动扫描仪超时")
            return {'status': 'error', 'message': '启动扫描仪超时'}

        if start_result != 0:
            self.log_error(f"启动扫描仪失败，返回码: {start_result}")
            return {'status': 'error', 'message': f'启动扫描仪失败，返回码: {start_result}'}

        self.log_info("扫描仪启动成功，开始捕获图像...")
        self.is_streaming = True
        self.scanner.start_continuous_capture(self._image_callback)

        # 启动显示处理线程
        self.display_processing_thread = threading.Thread(target=self._process_display_frames)
        self.display_processing_thread.daemon = True
        self.display_processing_thread.start()

        # 重置帧管理器统计
        self.frame_manager.reset_stats()

        return {'status': 'success', 'message': '视频流已启动'}

    def stop_stream(self) -> Dict[str, Any]:
        """停止视频流"""
        if not self.is_streaming:
            return {'status': 'success', 'message': '视频流未运行'}

        self.log_info("正在停止视频流...")
        self.is_streaming = False
        if self.scanner:
            self.scanner.stop_continuous_capture()

        if self.display_processing_thread and self.display_processing_thread.is_alive():
            self.display_processing_thread.join(timeout=2)

        self.log_info("视频流已停止")
        return {'status': 'success', 'message': '视频流已停止'}

    def _image_callback(self, width, height, format, size, out, ret):
        """硬件SDK的图像回调函数 - 优化版本"""
        if not self.is_streaming or ret != 0 or size == 0:
            return

        if format == 0:  # NV12
            try:
                # 使用帧管理器处理新帧
                raw_data = bytes(out)
                self.frame_manager.add_frame(raw_data, width, height)
            except Exception as e:
                self.log_error(f"处理图像回调时发生错误: {e}")

    def _process_display_frames(self):
        """处理显示帧的后台线程 - 优化版本"""
        self.log_info("显示帧处理线程已启动")
        frame_count = 0
        last_stats_time = time.time()

        while self.is_streaming:
            try:
                # 获取显示帧（已经是快速编码的JPEG）
                display_frame = self.frame_manager.get_display_frame(timeout=1.0)
                if display_frame:
                    # 直接发送到WebSocket，无需再次编码
                    async_to_sync(self.channel_layer.group_send)(
                        "scanner_stream",
                        {
                            "type": "send_image_data",
                            "image_bytes": display_frame['jpeg_data'],
                        }
                    )
                    frame_count += 1

                    # 定期输出性能统计
                    current_time = time.time()
                    if current_time - last_stats_time >= 5.0:  # 每5秒输出一次
                        stats = self.frame_manager.get_stats()
                        if stats:
                            self.log_info(f"显示流性能: {stats['fps']:.1f}fps, "
                                        f"丢帧率: {stats['drop_rate']:.1f}%, "
                                        f"队列: 显示{stats['display_queue_size']}/推理{stats['inference_queue_size']}")
                        last_stats_time = current_time

            except Exception as e:
                self.log_error(f"处理显示帧时发生错误: {e}")

        self.log_info("显示帧处理线程已停止")

    def get_latest_frame_for_inference(self) -> Optional[Dict[str, Any]]:
        """
        获取最新的高质量帧用于AI推理
        :return: 包含图像数据和元信息的字典，如果没有可用帧则返回None
        """
        if not self.is_streaming:
            return None

        return self.frame_manager.get_latest_inference_frame()

    def get_performance_stats(self) -> Optional[Dict[str, Any]]:
        """获取性能统计信息"""
        if not self.is_streaming:
            return None

        return self.frame_manager.get_stats()

    def get_status(self) -> Dict[str, Any]:
        """获取扫描仪当前状态"""
        status = {
            'is_connected': self.scanner is not None and self.scanner.running,
            'is_streaming': self.is_streaming,
            'device_ip': self.device_ip
        }

        # 如果正在流式传输，添加性能统计
        if self.is_streaming:
            stats = self.get_performance_stats()
            if stats:
                status['performance'] = stats

        return status