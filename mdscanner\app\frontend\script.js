// 连接到WebSocket服务器
const socket = io();

// 获取DOM元素
const startBtn = document.getElementById('startBtn');
const stopBtn = document.getElementById('stopBtn');
const videoStream = document.getElementById('videoStream');
const statusDiv = document.getElementById('status');
const fpsDisplay = document.getElementById('fpsDisplay');
const videoPlaceholder = document.getElementById('videoPlaceholder');

// 更新状态显示
function updateStatus(message, isSuccess = true) {
    statusDiv.textContent = message;
    statusDiv.className = isSuccess ? 'status-success' : 'status-error';
}

// 开始视频流
startBtn.addEventListener('click', () => {
    fetch('/start')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updateStatus(data.message, true);
                startBtn.disabled = true;
                stopBtn.disabled = false;
                videoPlaceholder.style.display = 'none';
                videoStream.style.display = 'block';
            } else {
                updateStatus(data.message, false);
            }
        })
        .catch(error => {
            console.error('启动视频流出错:', error);
            updateStatus('启动视频流出错: ' + error.message, false);
        });
});

// 停止视频流
stopBtn.addEventListener('click', () => {
    fetch('/stop')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updateStatus(data.message, true);
                startBtn.disabled = false;
                stopBtn.disabled = true;
                videoStream.src = ''; // 清除图像
                videoStream.style.display = 'none';
                videoPlaceholder.style.display = 'block';
                fpsDisplay.textContent = 'FPS: --'; // 重置FPS显示
            } else {
                updateStatus(data.message, false);
            }
        })
        .catch(error => {
            console.error('停止视频流出错:', error);
            updateStatus('停止视频流出错: ' + error.message, false);
        });
});

// 处理从服务器接收到的图像数据
socket.on('image_data', (data) => {
    // 将接收到的JPEG数据转换为Base64编码的URL
    const base64Image = 'data:image/jpeg;base64,' + btoa(
        new Uint8Array(data.image).reduce((data, byte) => data + String.fromCharCode(byte), '')
    );
    
    // 更新图像源
    videoStream.src = base64Image;
    
    // 更新FPS显示
    if (data.fps) {
        fpsDisplay.textContent = `FPS: ${data.fps}`;
    }
});

// 处理WebSocket连接事件
socket.on('connect', () => {
    console.log('已连接到服务器');
    updateStatus('已连接到服务器', true);
});

socket.on('disconnect', () => {
    console.log('与服务器断开连接');
    updateStatus('与服务器断开连接', false);
});

// 初始化按钮状态
startBtn.disabled = false;
stopBtn.disabled = true;