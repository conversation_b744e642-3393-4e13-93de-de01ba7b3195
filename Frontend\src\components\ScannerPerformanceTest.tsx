import React, { useState, useCallback, useRef } from 'react';
import { <PERSON><PERSON>, Card, Space, Typography, Statistic, Row, Col } from 'antd';
import { useScanner } from '../contexts/ScannerContext';

const { Title, Text } = Typography;

interface PerformanceStats {
  inferenceFrameCount: number;
  averageInferenceTime: number;
  maxInferenceTime: number;
  minInferenceTime: number;
  lastFrameTimestamp: number | null;
}

const ScannerPerformanceTest: React.FC = () => {
  const { isConnected, isStreaming, getInferenceFrame } = useScanner();
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [stats, setStats] = useState<PerformanceStats>({
    inferenceFrameCount: 0,
    averageInferenceTime: 0,
    maxInferenceTime: 0,
    minInferenceTime: 0,
    lastFrameTimestamp: null,
  });
  const [lastInferenceFrame, setLastInferenceFrame] = useState<string | null>(null);
  
  const testIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const inferenceTimes = useRef<number[]>([]);

  const startPerformanceTest = useCallback(() => {
    if (!isConnected || !isStreaming) {
      console.warn('Scanner not connected or streaming');
      return;
    }

    setIsTestRunning(true);
    inferenceTimes.current = [];
    setStats({
      inferenceFrameCount: 0,
      averageInferenceTime: 0,
      maxInferenceTime: 0,
      minInferenceTime: 0,
      lastFrameTimestamp: null,
    });

    // 每2秒获取一次推理帧
    testIntervalRef.current = setInterval(async () => {
      const startTime = performance.now();
      
      try {
        const frameData = await getInferenceFrame();
        const endTime = performance.now();
        const inferenceTime = endTime - startTime;
        
        inferenceTimes.current.push(inferenceTime);
        
        if (frameData) {
          setLastInferenceFrame(frameData);
          
          const currentStats = {
            inferenceFrameCount: inferenceTimes.current.length,
            averageInferenceTime: inferenceTimes.current.reduce((a, b) => a + b, 0) / inferenceTimes.current.length,
            maxInferenceTime: Math.max(...inferenceTimes.current),
            minInferenceTime: Math.min(...inferenceTimes.current),
            lastFrameTimestamp: Date.now(),
          };
          
          setStats(currentStats);
        }
      } catch (error) {
        console.error('Failed to get inference frame:', error);
      }
    }, 2000);
  }, [isConnected, isStreaming, getInferenceFrame]);

  const stopPerformanceTest = useCallback(() => {
    setIsTestRunning(false);
    if (testIntervalRef.current) {
      clearInterval(testIntervalRef.current);
      testIntervalRef.current = null;
    }
  }, []);

  const resetTest = useCallback(() => {
    stopPerformanceTest();
    setStats({
      inferenceFrameCount: 0,
      averageInferenceTime: 0,
      maxInferenceTime: 0,
      minInferenceTime: 0,
      lastFrameTimestamp: null,
    });
    setLastInferenceFrame(null);
    inferenceTimes.current = [];
  }, [stopPerformanceTest]);

  return (
    <Card title="扫描仪性能测试" style={{ margin: '16px' }}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        
        {/* 控制按钮 */}
        <Space>
          <Button 
            type="primary" 
            onClick={startPerformanceTest}
            disabled={!isConnected || !isStreaming || isTestRunning}
          >
            开始测试
          </Button>
          <Button 
            onClick={stopPerformanceTest}
            disabled={!isTestRunning}
          >
            停止测试
          </Button>
          <Button onClick={resetTest}>
            重置
          </Button>
        </Space>

        {/* 状态信息 */}
        <div>
          <Text strong>设备状态: </Text>
          <Text type={isConnected ? 'success' : 'danger'}>
            {isConnected ? '已连接' : '未连接'}
          </Text>
          <Text> | </Text>
          <Text strong>视频流: </Text>
          <Text type={isStreaming ? 'success' : 'warning'}>
            {isStreaming ? '运行中' : '已停止'}
          </Text>
          <Text> | </Text>
          <Text strong>测试状态: </Text>
          <Text type={isTestRunning ? 'success' : 'secondary'}>
            {isTestRunning ? '运行中' : '已停止'}
          </Text>
        </div>

        {/* 性能统计 */}
        <Row gutter={16}>
          <Col span={6}>
            <Statistic 
              title="推理帧数" 
              value={stats.inferenceFrameCount} 
              suffix="帧"
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="平均获取时间" 
              value={stats.averageInferenceTime} 
              precision={2}
              suffix="ms"
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="最大获取时间" 
              value={stats.maxInferenceTime} 
              precision={2}
              suffix="ms"
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="最小获取时间" 
              value={stats.minInferenceTime} 
              precision={2}
              suffix="ms"
            />
          </Col>
        </Row>

        {/* 最新推理帧显示 */}
        {lastInferenceFrame && (
          <Card title="最新推理帧" size="small">
            <div style={{ textAlign: 'center' }}>
              <img 
                src={lastInferenceFrame} 
                alt="Latest Inference Frame" 
                style={{ 
                  maxWidth: '400px', 
                  maxHeight: '300px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px'
                }} 
              />
              <div style={{ marginTop: '8px' }}>
                <Text type="secondary">
                  获取时间: {stats.lastFrameTimestamp ? new Date(stats.lastFrameTimestamp).toLocaleTimeString() : '未知'}
                </Text>
              </div>
            </div>
          </Card>
        )}

        {/* 使用说明 */}
        <Card title="使用说明" size="small" type="inner">
          <ul>
            <li>确保扫描仪已连接并启动视频流</li>
            <li>点击"开始测试"后，系统将每2秒获取一次高质量推理帧</li>
            <li>测试会显示推理帧获取的性能指标</li>
            <li>推理帧是高质量的，适合用于AI推理任务</li>
          </ul>
        </Card>
      </Space>
    </Card>
  );
};

export default ScannerPerformanceTest;
