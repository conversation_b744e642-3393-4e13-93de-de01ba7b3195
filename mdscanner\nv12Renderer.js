/**
 * NV12 WebGL Renderer for video frames
 */

class NV12Renderer {
  constructor(canvas) {
    this.canvas = canvas;
    this.gl = null;
    this.program = null;
    this.positionLocation = null;
    this.texCoordLocation = null;
    this.yTextureLocation = null;
    this.uvTextureLocation = null;
    this.yTexture = null;
    this.uvTexture = null;
    this.texCoordBuffer = null;
    this.positionBuffer = null;
    this.initialized = false;
  }

  init() {
    this.gl = this.canvas.getContext('webgl');
    if (!this.gl) {
      console.error('WebGL not supported');
      return false;
    }

    // Create shader program
    const vertexShaderSource = `
      attribute vec4 a_position;
      attribute vec2 a_texCoord;
      varying vec2 v_texCoord;
      void main() {
        gl_Position = a_position;
        v_texCoord = a_texCoord;
      }
    `;

    const fragmentShaderSource = `
      precision mediump float;
      uniform sampler2D y_texture;
      uniform sampler2D uv_texture;
      varying vec2 v_texCoord;
      void main() {
        float y = texture2D(y_texture, v_texCoord).r;
        // Correctly sample U from the Red channel and V from the Alpha channel
        vec2 uv = texture2D(uv_texture, v_texCoord).ra - 0.5;
        
        // YUV to RGB conversion
        vec3 rgb;
        rgb.r = y + 1.402 * uv.y;
        rgb.g = y - 0.344136 * uv.x - 0.714136 * uv.y;
        rgb.b = y + 1.772 * uv.x;
        
        gl_FragColor = vec4(rgb, 1.0);
      }
    `;

    // Create shaders
    const vertexShader = this.compileShader(this.gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = this.compileShader(this.gl.FRAGMENT_SHADER, fragmentShaderSource);

    // Create program
    this.program = this.gl.createProgram();
    this.gl.attachShader(this.program, vertexShader);
    this.gl.attachShader(this.program, fragmentShader);
    this.gl.linkProgram(this.program);

    if (!this.gl.getProgramParameter(this.program, this.gl.LINK_STATUS)) {
      console.error('Could not link shader program');
      return false;
    }

    // Get attribute and uniform locations
    this.positionLocation = this.gl.getAttribLocation(this.program, 'a_position');
    this.texCoordLocation = this.gl.getAttribLocation(this.program, 'a_texCoord');
    this.yTextureLocation = this.gl.getUniformLocation(this.program, 'y_texture');
    this.uvTextureLocation = this.gl.getUniformLocation(this.program, 'uv_texture');

    // Create buffers
    this.positionBuffer = this.gl.createBuffer();
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.positionBuffer);
    this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array([
      -1.0, -1.0,
       1.0, -1.0,
      -1.0,  1.0,
       1.0,  1.0
    ]), this.gl.STATIC_DRAW);

    this.texCoordBuffer = this.gl.createBuffer();
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.texCoordBuffer);
    this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array([
      0.0, 1.0,
      1.0, 1.0,
      0.0, 0.0,
      1.0, 0.0
    ]), this.gl.STATIC_DRAW);

    // Create textures
    this.yTexture = this.createTexture();
    this.uvTexture = this.createTexture();
    
    this.initialized = true;
    return true;
  }

  compileShader(type, source) {
    const shader = this.gl.createShader(type);
    this.gl.shaderSource(shader, source);
    this.gl.compileShader(shader);

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      console.error('Shader compilation error:', this.gl.getShaderInfoLog(shader));
      this.gl.deleteShader(shader);
      return null;
    }

    return shader;
  }

  createTexture() {
    const texture = this.gl.createTexture();
    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);
    return texture;
  }

  render(buffer, width, height) {
    if (!this.initialized) {
      if (!this.init()) return;
    }
    
    // Resize canvas if needed
    if (this.canvas.width !== width || this.canvas.height !== height) {
      this.canvas.width = width;
      this.canvas.height = height;
      this.gl.viewport(0, 0, width, height);
    }
    
    const imageData = new Uint8Array(buffer);
    
    // Set Y plane data (first 2/3 of the buffer for NV12)
    const ySize = width * height;
    const yData = imageData.subarray(0, ySize);
    
    // Set UV plane data (remaining 1/3 of the buffer)
    const uvData = imageData.subarray(ySize);
    
    this.gl.useProgram(this.program);
    
    // Update Y texture
    this.gl.activeTexture(this.gl.TEXTURE0);
    this.gl.bindTexture(this.gl.TEXTURE_2D, this.yTexture);
    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.LUMINANCE, width, height, 0, this.gl.LUMINANCE, this.gl.UNSIGNED_BYTE, yData);
    this.gl.uniform1i(this.yTextureLocation, 0);
    
    // Update UV texture
    this.gl.activeTexture(this.gl.TEXTURE1);
    this.gl.bindTexture(this.gl.TEXTURE_2D, this.uvTexture);
    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.LUMINANCE_ALPHA, width / 2, height / 2, 0, this.gl.LUMINANCE_ALPHA, this.gl.UNSIGNED_BYTE, uvData);
    this.gl.uniform1i(this.uvTextureLocation, 1);
    
    // Set up position attribute
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.positionBuffer);
    this.gl.enableVertexAttribArray(this.positionLocation);
    this.gl.vertexAttribPointer(this.positionLocation, 2, this.gl.FLOAT, false, 0, 0);
    
    // Set up texture coordinate attribute
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.texCoordBuffer);
    this.gl.enableVertexAttribArray(this.texCoordLocation);
    this.gl.vertexAttribPointer(this.texCoordLocation, 2, this.gl.FLOAT, false, 0, 0);
    
    // Draw
    this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);
  }

  destroy() {
    if (!this.gl) return;
    
    // Delete textures
    if (this.yTexture) this.gl.deleteTexture(this.yTexture);
    if (this.uvTexture) this.gl.deleteTexture(this.uvTexture);
    
    // Delete buffers
    if (this.positionBuffer) this.gl.deleteBuffer(this.positionBuffer);
    if (this.texCoordBuffer) this.gl.deleteBuffer(this.texCoordBuffer);
    
    // Delete program and attached shaders
    if (this.program) {
      const shaders = this.gl.getAttachedShaders(this.program);
      if (shaders) {
        shaders.forEach(shader => {
          this.gl.detachShader(this.program, shader);
          this.gl.deleteShader(shader);
        });
      }
      this.gl.deleteProgram(this.program);
    }
    
    this.initialized = false;
  }
}

export default NV12Renderer; 