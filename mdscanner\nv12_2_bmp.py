import os
import numpy as np
import imageio
import argparse

def nv12_to_bmp(nv12_file_path, bmp_file_path, width=1024, height=1280):
    """
    读取 NV12 格式的图像文件并转换为 BMP 格式
    :param nv12_file_path: 输入 NV12 文件路径
    :param bmp_file_path: 输出 BMP 文件路径
    :param width: 图像宽度
    :param height: 图像高度
    """
    try:
        # 计算各分量的大小
        y_size = width * height
        uv_size = (width // 2) * (height // 2) * 2  # NV12的UV是交错存储，所以是*2
        total_size = y_size + uv_size
        
        print(f"预期文件大小: {total_size} 字节")
        
        # 读取 NV12 文件数据
        with open(nv12_file_path, 'rb') as f:
            nv12_data = f.read()

        print(f"实际文件大小: {len(nv12_data)} 字节")
        
        if len(nv12_data) != total_size:
            print(f"文件大小匹配: {len(nv12_data) == total_size}")
        
        # 提取Y分量
        y_data = np.frombuffer(nv12_data[:y_size], dtype=np.uint8)
        y_plane = y_data.reshape((height, width))
        
        # 提取UV分量（交错存储）
        uv_data = np.frombuffer(nv12_data[y_size:], dtype=np.uint8)
        uv_plane = uv_data.reshape((height // 2, width // 2, 2))
        
        # 分离U和V分量
        u_plane = uv_plane[:, :, 0]  # U分量
        v_plane = uv_plane[:, :, 1]  # V分量
        
        print(f"Y分量范围: {y_plane.min()} - {y_plane.max()}")
        print(f"U分量范围: {u_plane.min()} - {u_plane.max()}")
        print(f"V分量范围: {v_plane.min()} - {v_plane.max()}")
        
        # 上采样U和V分量到全分辨率
        u_upsampled = np.repeat(np.repeat(u_plane, 2, axis=0), 2, axis=1)
        v_upsampled = np.repeat(np.repeat(v_plane, 2, axis=0), 2, axis=1)
        
        # 确保尺寸匹配
        u_upsampled = u_upsampled[:height, :width]
        v_upsampled = v_upsampled[:height, :width]
        
        # NV12到RGB转换
        # 按照js代码中的转换公式
        y = y_plane.astype(np.float32) / 255.0
        u = (u_upsampled.astype(np.float32) / 255.0) - 0.5
        v = (v_upsampled.astype(np.float32) / 255.0) - 0.5
        
        # 转换公式（与js代码一致）
        r = y + 1.402 * v
        g = y - 0.344136 * u - 0.714136 * v
        b = y + 1.772 * u
        
        # 限制在0-1范围内，然后转换为0-255
        r = np.clip(r, 0, 1) * 255
        g = np.clip(g, 0, 1) * 255
        b = np.clip(b, 0, 1) * 255
        
        # 转换为uint8
        r = r.astype(np.uint8)
        g = g.astype(np.uint8)
        b = b.astype(np.uint8)
        
        # 组合成RGB图像
        rgb_image = np.stack([r, g, b], axis=2)
        
        # 保存为 BMP 格式
        imageio.imwrite(bmp_file_path, rgb_image)
        print(f"成功转换 {nv12_file_path} -> {bmp_file_path}")
        print(f"图像尺寸: {width}x{height}")

    except Exception as e:
        print(f"错误: 无法处理 {nv12_file_path}: {e}")
        import traceback
        traceback.print_exc()

def convert_nv12_folder(input_folder, output_folder, width=1024, height=1280):
    """
    转换指定文件夹中的所有 NV12 图像为 BMP 格式
    :param input_folder: 输入文件夹路径
    :param output_folder: 输出文件夹路径
    :param width: 图像宽度
    :param height: 图像高度
    """
    # 获取输入文件夹中的所有 .raw 文件（实际是NV12格式）
    nv12_files = [f for f in os.listdir(input_folder) if f.endswith('.raw')]

    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 遍历每个 NV12 文件并转换
    for nv12_file in nv12_files:
        nv12_file_path = os.path.join(input_folder, nv12_file)
        bmp_file_name = os.path.splitext(nv12_file)[0] + '.bmp'
        bmp_file_path = os.path.join(output_folder, bmp_file_name)

        # 转换并保存 BMP
        nv12_to_bmp(nv12_file_path, bmp_file_path, width, height)

def parse_arguments():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(
        description='将NV12格式图像转换为BMP格式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python yuv2bmp.py -i /path/to/nv12/folder
  python yuv2bmp.py -i /path/to/nv12/folder -o /path/to/output/folder
        '''
    )
    
    parser.add_argument(
        '-i', '--input',
        required=True,
        help='输入文件夹路径（包含NV12文件）'
    )
    
    parser.add_argument(
        '-o', '--output',
        help='输出文件夹路径（保存BMP文件，默认为输入文件夹下的BMP子文件夹）'
    )
    
    parser.add_argument(
        '-w', '--width',
        type=int,
        default=1024,
        help='图像宽度（默认：1024）'
    )
    
    parser.add_argument(
        '--height',
        type=int,
        default=1280,
        help='图像高度（默认：1280）'
    )
    
    return parser.parse_args()

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_arguments()
    
    # 验证输入文件夹是否存在
    if not os.path.exists(args.input):
        print(f"错误: 输入文件夹不存在: {args.input}")
        exit(1)
    
    if not os.path.isdir(args.input):
        print(f"错误: 输入路径不是文件夹: {args.input}")
        exit(1)
    
    # 设置输出文件夹
    if args.output:
        output_folder = args.output
    else:
        output_folder = os.path.join(args.input, "BMP")
    
    print(f"输入文件夹: {args.input}")
    print(f"输出文件夹: {output_folder}")
    print(f"图像尺寸: {args.width}x{args.height}")
    print(f"格式: NV12")
    print("-" * 50)
    
    # 转换文件夹中的所有 NV12 -> BMP 图像
    convert_nv12_folder(args.input, output_folder, args.width, args.height) 