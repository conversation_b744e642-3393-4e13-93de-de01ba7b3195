import os
import sys
import time
import threading
import datetime
import statistics

# -- 配置Django环境 --
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_project.settings')
import django
django.setup()
# -- Django环境配置结束 --

from vision_app.services.scanner_service import ScannerService

# --- 配置 ---
DEVICE_IP = "************"  # 修改为你的设备IP地址
TEST_DURATION = 30  # 测试持续时间（秒）

# --- 性能统计 ---
class PerformanceStats:
    def __init__(self):
        self.display_frame_times = []
        self.inference_frame_times = []
        self.display_frame_count = 0
        self.inference_frame_count = 0
        self.start_time = None
        self.last_frame_time = None

    def start_test(self):
        """开始测试计时"""
        self.start_time = time.time()
        self.last_frame_time = self.start_time

    def record_display_frame(self):
        current_time = time.time()
        if self.last_frame_time:
            frame_interval = current_time - self.last_frame_time
            self.display_frame_times.append(frame_interval)
        self.display_frame_count += 1
        self.last_frame_time = current_time

    def record_inference_frame(self):
        self.inference_frame_count += 1

    def get_stats(self):
        if not self.start_time:
            return None

        duration = time.time() - self.start_time
        display_fps = self.display_frame_count / duration if duration > 0 else 0
        inference_fps = self.inference_frame_count / duration if duration > 0 else 0

        avg_frame_interval = statistics.mean(self.display_frame_times) if self.display_frame_times else 0

        return {
            'duration': duration,
            'display_frames': self.display_frame_count,
            'inference_frames': self.inference_frame_count,
            'display_fps': display_fps,
            'inference_fps': inference_fps,
            'avg_frame_interval': avg_frame_interval,
            'target_fps': 5.0
        }

def test_scanner_performance():
    """测试扫描仪性能"""
    print(f"开始扫描仪性能测试...")
    print(f"设备IP: {DEVICE_IP}")
    print(f"测试时长: {TEST_DURATION}秒")
    print(f"目标显示帧率: 5 FPS")
    print("-" * 50)
    
    scanner_service = ScannerService()
    stats = PerformanceStats()
    
    # 模拟WebSocket消息处理
    def mock_websocket_handler():
        """模拟WebSocket消息处理"""
        while stats.start_time and (time.time() - stats.start_time) < TEST_DURATION:
            # 检查是否有新的推理帧
            inference_frame = scanner_service.get_latest_inference_frame()
            if inference_frame:
                stats.record_inference_frame()

            time.sleep(0.1)  # 100ms检查间隔
    
    try:
        # 1. 连接设备
        print("正在连接设备...")
        result = scanner_service.connect(DEVICE_IP)
        if result['status'] != 'success':
            print(f"连接失败: {result['message']}")
            return
        print("设备连接成功!")
        
        # 2. 启动视频流
        print("正在启动视频流...")
        result = scanner_service.start_stream()
        if result['status'] != 'success':
            print(f"启动视频流失败: {result['message']}")
            return
        print("视频流启动成功!")
        
        # 3. 开始性能测试
        print(f"开始性能测试，持续{TEST_DURATION}秒...")
        stats.start_test()  # 使用新的开始方法

        # 启动模拟WebSocket处理线程
        websocket_thread = threading.Thread(target=mock_websocket_handler)
        websocket_thread.daemon = True
        websocket_thread.start()

        # 主循环：模拟显示帧处理
        while (time.time() - stats.start_time) < TEST_DURATION:
            # 模拟显示帧处理（每200ms检查一次，对应5FPS）
            time.sleep(0.2)
            stats.record_display_frame()

            # 实时显示状态
            if stats.display_frame_count % 10 == 0:
                current_stats = stats.get_stats()
                if current_stats:
                    print(f"进度: {current_stats['duration']:.1f}s | "
                          f"显示帧: {current_stats['display_frames']} | "
                          f"推理帧: {current_stats['inference_frames']} | "
                          f"显示FPS: {current_stats['display_fps']:.2f}")
        
        # 4. 停止视频流
        print("\n正在停止视频流...")
        scanner_service.stop_stream()
        
        # 5. 输出性能统计
        final_stats = stats.get_stats()
        print("\n" + "=" * 50)
        print("性能测试结果:")
        print("=" * 50)
        print(f"测试时长: {final_stats['duration']:.2f} 秒")
        print(f"显示帧总数: {final_stats['display_frames']}")
        print(f"推理帧总数: {final_stats['inference_frames']}")
        print(f"显示帧率: {final_stats['display_fps']:.2f} FPS (目标: {final_stats['target_fps']} FPS)")
        print(f"推理帧率: {final_stats['inference_fps']:.2f} FPS")
        print(f"平均帧间隔: {final_stats['avg_frame_interval']*1000:.1f} ms")
        
        # 性能评估
        fps_efficiency = (final_stats['display_fps'] / final_stats['target_fps']) * 100
        print(f"帧率效率: {fps_efficiency:.1f}%")
        
        if fps_efficiency >= 90:
            print("✅ 性能优秀!")
        elif fps_efficiency >= 70:
            print("⚠️  性能良好，但有改进空间")
        else:
            print("❌ 性能需要优化")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        try:
            scanner_service.disconnect()
            print("设备连接已断开")
        except:
            pass

if __name__ == "__main__":
    test_scanner_performance()
