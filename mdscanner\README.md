# 视频流展示应用

这个应用可以从网段设备上不断获取图像，并将图像转换为视频流在浏览器中展示。

## 功能

- 通过TCP与设备通信，获取图像数据。
- 将NV12格式的图像数据转换为JPEG格式。
- 通过WebSocket将图像数据流式传输到前端。
- 在浏览器中实时显示视频流。

## 目录结构

```
mdscanner/
├── mdscanner.py          # 与设备通信的客户端
├── nv12_2_bmp.py         # NV12到BMP的转换脚本
├── nv12Renderer.js       # NV12渲染器（未使用）
├── output_image/         # 图像输出目录
├── app/
│   ├── backend/
│   │   ├── __init__.py
│   │   ├── server.py       # 后端服务器
│   │   ├── image_processor.py # 图像处理
│   ├── frontend/
│   │   ├── index.html      # 前端页面
│   │   ├── style.css       # 样式
│   │   ├── script.js       # 前端脚本
├── requirements.txt       # 依赖库
├── README.md              # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 修改 `app/backend/server.py` 中的 `DEVICE_IP` 变量，设置为你的设备IP地址。

## 运行

```bash
cd app/backend
python server.py
```

服务器将在 `http://localhost:5000` 上运行。

## 使用

1. 打开浏览器，访问 `http://localhost:5000`。
2. 点击“开始”按钮启动视频流。
3. 点击“停止”按钮停止视频流。

## 注意事项

- 确保设备已连接到网络，并且IP地址正确。
- 确保设备支持通过TCP获取图像数据。
- 应用假设设备返回的图像数据为NV12格式。