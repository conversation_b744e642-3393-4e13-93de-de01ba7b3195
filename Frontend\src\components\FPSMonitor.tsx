import React, { memo } from 'react';
import { Card, Statistic, Row, Col, Badge, Typography } from 'antd';
import { useFPSMonitor } from '../hooks/useFPSMonitor';

const { Text } = Typography;

interface FPSMonitorProps {
  enabled?: boolean;
  compact?: boolean;
  style?: React.CSSProperties;
}

const FPSMonitor: React.FC<FPSMonitorProps> = memo(({ 
  enabled = true, 
  compact = false,
  style 
}) => {
  const { fpsStats, resetStats } = useFPSMonitor({ 
    enabled,
    updateInterval: 500, // 更频繁的更新
    maxSamples: 30 
  });

  const getFPSColor = (fps: number) => {
    if (fps >= 25) return '#52c41a'; // 绿色 - 优秀
    if (fps >= 15) return '#faad14'; // 黄色 - 良好
    if (fps >= 10) return '#fa8c16'; // 橙色 - 一般
    return '#f5222d'; // 红色 - 差
  };

  const getFPSStatus = (fps: number) => {
    if (fps >= 25) return 'success';
    if (fps >= 15) return 'warning';
    return 'error';
  };

  if (compact) {
    return (
      <div style={{ 
        position: 'fixed', 
        top: 10, 
        right: 10, 
        zIndex: 1000,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        fontSize: '12px',
        fontFamily: 'monospace',
        ...style 
      }}>
        <div>FPS: <span style={{ color: getFPSColor(fpsStats.currentFPS) }}>
          {fpsStats.currentFPS.toFixed(1)}
        </span></div>
        <div>AVG: {fpsStats.averageFPS.toFixed(1)}</div>
        <div>Frames: {fpsStats.frameCount}</div>
        {fpsStats.droppedFrames > 0 && (
          <div style={{ color: '#f5222d' }}>
            Dropped: {fpsStats.droppedFrames}
          </div>
        )}
      </div>
    );
  }

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span>前端渲染性能监控</span>
          <Badge 
            status={getFPSStatus(fpsStats.currentFPS)} 
            text={`${fpsStats.currentFPS.toFixed(1)} FPS`}
          />
        </div>
      }
      size="small"
      style={{ width: '100%', ...style }}
      extra={
        <Text 
          type="secondary" 
          style={{ cursor: 'pointer' }}
          onClick={resetStats}
        >
          重置
        </Text>
      }
    >
      <Row gutter={16}>
        <Col span={6}>
          <Statistic
            title="当前FPS"
            value={fpsStats.currentFPS}
            precision={1}
            valueStyle={{ color: getFPSColor(fpsStats.currentFPS) }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="平均FPS"
            value={fpsStats.averageFPS}
            precision={1}
            valueStyle={{ color: getFPSColor(fpsStats.averageFPS) }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="最小FPS"
            value={fpsStats.minFPS}
            precision={1}
            valueStyle={{ color: getFPSColor(fpsStats.minFPS) }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="最大FPS"
            value={fpsStats.maxFPS}
            precision={1}
            valueStyle={{ color: getFPSColor(fpsStats.maxFPS) }}
          />
        </Col>
      </Row>
      
      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={8}>
          <Statistic
            title="总帧数"
            value={fpsStats.frameCount}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="丢帧数"
            value={fpsStats.droppedFrames}
            valueStyle={{ color: fpsStats.droppedFrames > 0 ? '#f5222d' : undefined }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="渲染时间"
            value={fpsStats.renderTime}
            precision={2}
            suffix="ms"
            valueStyle={{ 
              color: fpsStats.renderTime > 16 ? '#fa8c16' : '#52c41a' 
            }}
          />
        </Col>
      </Row>

      <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
        <div>• 绿色: 优秀 (≥25 FPS) | 黄色: 良好 (15-24 FPS) | 红色: 需优化 (&lt;15 FPS)</div>
        <div>• 渲染时间 &gt;16ms 表示可能影响60FPS目标</div>
        <div>• 丢帧: 帧间隔超过33ms (低于30FPS)</div>
      </div>
    </Card>
  );
});

FPSMonitor.displayName = 'FPSMonitor';

export default FPSMonitor;
