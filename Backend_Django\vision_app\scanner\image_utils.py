import numpy as np
import cv2
from io import BytesIO

def nv12_to_jpeg(nv12_data, width, height, quality=90, fast_mode=False):
    """
    将NV12格式的图像数据高效地转换为JPEG格式。
    :param nv12_data: NV12格式的原始图像字节数据
    :param width: 图像宽度
    :param height: 图像高度
    :param quality: JPEG质量 (1-100)，默认90
    :param fast_mode: 快速模式，降低质量但提升速度
    :return: JPEG格式的图像数据 (BytesIO)，如果失败则返回 None
    """
    try:
        # NV12 图像数据的大小应为 width * height * 1.5
        expected_size = width * height * 3 // 2
        if len(nv12_data) != expected_size:
            raise ValueError(f"NV12 data size mismatch: expected {expected_size}, got {len(nv12_data)}")

        # 将字节数据转换为 NumPy 数组
        yuv_data = np.frombuffer(nv12_data, dtype=np.uint8)

        # 重塑为 OpenCV 处理 NV12 所需的形状（高度为原始高度的1.5倍）
        yuv_image = yuv_data.reshape((height * 3 // 2, width))

        # 使用 OpenCV 将 NV12 颜色空间转换为 BGR
        bgr_image = cv2.cvtColor(yuv_image, cv2.COLOR_YUV2BGR_NV12)

        # 根据模式调整编码参数
        if fast_mode:
            # 快速模式：降低质量，启用快速编码
            encode_params = [
                int(cv2.IMWRITE_JPEG_QUALITY), max(30, quality // 2),  # 降低质量
                int(cv2.IMWRITE_JPEG_OPTIMIZE), 0,  # 禁用优化以提升速度
                int(cv2.IMWRITE_JPEG_PROGRESSIVE), 0  # 禁用渐进式编码
            ]
        else:
            # 高质量模式：保持原有质量
            encode_params = [
                int(cv2.IMWRITE_JPEG_QUALITY), quality,
                int(cv2.IMWRITE_JPEG_OPTIMIZE), 1
            ]

        # 将 BGR 图像编码为 JPEG
        is_success, jpeg_buffer = cv2.imencode('.jpg', bgr_image, encode_params)

        if not is_success:
            raise RuntimeError("Failed to encode image to JPEG")

        return BytesIO(jpeg_buffer.tobytes())

    except Exception as e:
        print(f"NV12 to JPEG conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def nv12_to_jpeg_dual_quality(nv12_data, width, height):
    """
    将NV12数据同时转换为两种质量的JPEG：
    - 高质量版本用于AI推理
    - 低质量版本用于实时显示
    :return: (high_quality_jpeg_io, low_quality_jpeg_io) 元组
    """
    try:
        # 共享BGR转换结果以节省计算
        expected_size = width * height * 3 // 2
        if len(nv12_data) != expected_size:
            raise ValueError(f"NV12 data size mismatch: expected {expected_size}, got {len(nv12_data)}")

        yuv_data = np.frombuffer(nv12_data, dtype=np.uint8)
        yuv_image = yuv_data.reshape((height * 3 // 2, width))
        bgr_image = cv2.cvtColor(yuv_image, cv2.COLOR_YUV2BGR_NV12)

        # 高质量版本 (用于AI推理)
        high_quality_params = [
            int(cv2.IMWRITE_JPEG_QUALITY), 95,
            int(cv2.IMWRITE_JPEG_OPTIMIZE), 1
        ]
        is_success_hq, hq_buffer = cv2.imencode('.jpg', bgr_image, high_quality_params)

        # 低质量版本 (用于实时显示)
        low_quality_params = [
            int(cv2.IMWRITE_JPEG_QUALITY), 60,
            int(cv2.IMWRITE_JPEG_OPTIMIZE), 0,
            int(cv2.IMWRITE_JPEG_PROGRESSIVE), 0
        ]
        is_success_lq, lq_buffer = cv2.imencode('.jpg', bgr_image, low_quality_params)

        if not (is_success_hq and is_success_lq):
            raise RuntimeError("Failed to encode image to JPEG")

        return (
            BytesIO(hq_buffer.tobytes()) if is_success_hq else None,
            BytesIO(lq_buffer.tobytes()) if is_success_lq else None
        )

    except Exception as e:
        print(f"Dual quality NV12 to JPEG conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None