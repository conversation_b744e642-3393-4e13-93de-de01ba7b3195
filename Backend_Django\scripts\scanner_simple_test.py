import os
import sys
import time

# -- 配置Django环境 --
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_project.settings')
import django
django.setup()
# -- Django环境配置结束 --

from vision_app.services import scanner_service_instance

# --- 配置 ---
DEVICE_IP = "************"  # 修改为你的设备IP地址

def test_scanner_basic():
    """测试扫描仪基本功能"""
    print(f"开始扫描仪基本功能测试...")
    print(f"设备IP: {DEVICE_IP}")
    print("-" * 40)
    
    try:
        # 1. 检查初始状态
        print("1. 检查初始状态...")
        status = scanner_service_instance.get_status()
        print(f"   连接状态: {status['is_connected']}")
        print(f"   推流状态: {status['is_streaming']}")
        
        # 2. 连接设备
        print("\n2. 连接设备...")
        result = scanner_service_instance.connect(DEVICE_IP)
        print(f"   结果: {result['status']} - {result['message']}")
        
        if result['status'] != 'success':
            print("连接失败，测试终止")
            return
        
        # 3. 检查连接后状态
        print("\n3. 检查连接后状态...")
        status = scanner_service_instance.get_status()
        print(f"   连接状态: {status['is_connected']}")
        print(f"   设备IP: {status['device_ip']}")
        
        # 4. 启动视频流
        print("\n4. 启动视频流...")
        result = scanner_service_instance.start_stream()
        print(f"   结果: {result['status']} - {result['message']}")
        
        if result['status'] != 'success':
            print("启动视频流失败")
            return
        
        # 5. 检查推流状态
        print("\n5. 检查推流状态...")
        status = scanner_service_instance.get_status()
        print(f"   推流状态: {status['is_streaming']}")
        if 'display_queue_size' in status:
            print(f"   显示队列大小: {status['display_queue_size']}")
            print(f"   推理队列大小: {status['inference_queue_size']}")
            print(f"   跳过帧数: {status['frames_skipped']}")
            print(f"   有推理帧: {status['has_inference_frame']}")
        
        # 6. 等待几秒钟收集数据
        print("\n6. 等待5秒钟收集数据...")
        for i in range(5):
            time.sleep(1)
            status = scanner_service_instance.get_status()
            inference_frame = scanner_service_instance.get_latest_inference_frame()
            print(f"   第{i+1}秒: 显示队列={status.get('display_queue_size', 'N/A')}, "
                  f"推理队列={status.get('inference_queue_size', 'N/A')}, "
                  f"推理帧={'有' if inference_frame else '无'}")
        
        # 7. 停止视频流
        print("\n7. 停止视频流...")
        result = scanner_service_instance.stop_stream()
        print(f"   结果: {result['status']} - {result['message']}")
        
        # 8. 断开连接
        print("\n8. 断开连接...")
        result = scanner_service_instance.disconnect()
        print(f"   结果: {result['status']} - {result['message']}")
        
        # 9. 检查最终状态
        print("\n9. 检查最终状态...")
        status = scanner_service_instance.get_status()
        print(f"   连接状态: {status['is_connected']}")
        print(f"   推流状态: {status['is_streaming']}")
        
        print("\n✅ 基本功能测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保清理资源
        try:
            scanner_service_instance.disconnect()
        except:
            pass

if __name__ == "__main__":
    test_scanner_basic()
